/**
 * Lucide Icon Manager
 * A comprehensive solution to manage Lucide icons and prevent errors
 */

(function() {
  // Store original Lucide methods
  let originalCreateIcons = null;

  // Track processed elements to prevent double processing
  const processedElements = new WeakSet();

  // List of critical icons that should be handled by the sprite system
  const criticalIconsList = [
    "panel-left", "chevron-down", "chevron-right", "chevron-up", "chevron-left",
    "menu", "home", "file-text", "users", "settings", "log-out", "bell",
    "alert-triangle", "alert-circle", "check-circle", "x-circle", "info",
    "x", "check", "plus", "minus", "edit", "trash", "copy", "sun", "moon",
    "monitor", "search", "user", "calendar", "clock", "bar-chart",
    "chevrons-up-down", "globe", "badge", "bar-chart-3", "users-x",
    "id-card", "file-bar-chart", "repeat", "heart-pulse", "grid",
    "chart-pie", "trending-up"
  ];

  // Make the list available globally
  window.criticalIcons = criticalIconsList;

  /**
   * Patch the Lucide library to prevent errors with sprite icons
   */
  function patchLucide() {
    // Skip if already patched
    if (window.lucideManagerPatched) return;

    // Wait for Lucide to be available
    if (!window.lucide || !window.lucide.createIcons) return;

    // Store the original method
    originalCreateIcons = window.lucide.createIcons;

    // Replace with our patched version
    window.lucide.createIcons = function(options = {}) {
      // Handle specific elements
      if (options && options.elements) {
        // Filter out elements that should be handled by the sprite system
        const filteredElements = Array.from(options.elements).filter(element => {
          // Skip if already processed
          if (processedElements.has(element)) return false;

          // Mark as processed
          processedElements.add(element);

          // Get the icon name
          const iconName = element.getAttribute('data-lucide');

          // Skip critical icons (handled by sprite)
          if (iconName && criticalIconsList.includes(iconName)) {
            // Create sprite icon instead
            createSpriteIcon(element, iconName);
            return false;
          }

          // Include this element for Lucide processing
          return true;
        });

        // Only call original if we have elements to process
        if (filteredElements.length > 0) {
          // Call the original method with filtered elements
          originalCreateIcons({
            ...options,
            elements: filteredElements
          });
        }
      }
      // Handle root element
      else if (options && options.root) {
        // Find all icon elements in the root
        const iconElements = options.root.querySelectorAll('[data-lucide]');

        // Process each element
        const elementsToProcess = Array.from(iconElements).filter(element => {
          // Skip if already processed
          if (processedElements.has(element)) return false;

          // Mark as processed
          processedElements.add(element);

          // Get the icon name
          const iconName = element.getAttribute('data-lucide');

          // Skip critical icons (handled by sprite)
          if (iconName && criticalIconsList.includes(iconName)) {
            // Create sprite icon instead
            createSpriteIcon(element, iconName);
            return false;
          }

          // Include this element for Lucide processing
          return true;
        });

        // Only call original if we have elements to process
        if (elementsToProcess.length > 0) {
          // Call the original method with filtered elements
          originalCreateIcons({
            ...options,
            elements: elementsToProcess
          });
        }
      }
      // Default case (no options)
      else {
        // Find all icon elements in the document
        const iconElements = document.querySelectorAll('[data-lucide]');

        // Process each element
        const elementsToProcess = Array.from(iconElements).filter(element => {
          // Skip if already processed
          if (processedElements.has(element)) return false;

          // Mark as processed
          processedElements.add(element);

          // Get the icon name
          const iconName = element.getAttribute('data-lucide');

          // Skip critical icons (handled by sprite)
          if (iconName && criticalIconsList.includes(iconName)) {
            // Create sprite icon instead
            createSpriteIcon(element, iconName);
            return false;
          }

          // Include this element for Lucide processing
          return true;
        });

        // Only call original if we have elements to process
        if (elementsToProcess.length > 0) {
          // Call the original method with filtered elements
          originalCreateIcons({
            elements: elementsToProcess
          });
        }
      }
    };

    // Mark as patched
    window.lucideManagerPatched = true;
    console.log('Lucide Manager: Lucide library patched successfully');
  }

  /**
   * Create a sprite icon for a critical icon
   */
  function createSpriteIcon(element, iconName) {
    // Skip if the element is not found or already an SVG
    if (!element || element.tagName.toLowerCase() === 'svg') return;

    // Create the SVG element
    const svg = document.createElementNS('http://www.w3.org/2000/svg', 'svg');

    // Copy attributes from the original element
    const size = element.style.width ? parseInt(element.style.width) : 24;
    const color = element.style.color || 'currentColor';
    const strokeWidth = element.getAttribute('stroke-width') || 2;

    // Set attributes
    svg.setAttribute('width', size);
    svg.setAttribute('height', size);
    svg.setAttribute('stroke', color);
    svg.setAttribute('stroke-width', strokeWidth);
    svg.setAttribute('fill', 'none');
    svg.setAttribute('stroke-linecap', 'round');
    svg.setAttribute('stroke-linejoin', 'round');
    svg.setAttribute('class', element.className ? element.className + ' sprite-icon' : 'sprite-icon');
    svg.setAttribute('data-sprite-icon', iconName);

    // Create the use element
    const use = document.createElementNS('http://www.w3.org/2000/svg', 'use');
    use.setAttributeNS('http://www.w3.org/1999/xlink', 'xlink:href', `#${iconName}`);
    svg.appendChild(use);

    // Replace the original element with the sprite icon
    if (element.parentNode) {
      element.parentNode.replaceChild(svg, element);
    }
  }

  /**
   * Initialize all icons in the document
   */
  function initIcons() {
    // Ensure Lucide is patched
    patchLucide();

    // Process all icons
    if (window.lucide && window.lucide.createIcons) {
      window.lucide.createIcons();
    }
  }

  // Try to patch Lucide immediately
  patchLucide();

  // Also try when the DOM is ready
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', patchLucide);
    document.addEventListener('DOMContentLoaded', initIcons);
  } else {
    patchLucide();
    initIcons();
  }

  // Check periodically for Lucide
  const checkInterval = setInterval(() => {
    if (window.lucide) {
      patchLucide();
      clearInterval(checkInterval);
    }
  }, 100);

  // Stop checking after 10 seconds
  setTimeout(() => {
    clearInterval(checkInterval);
  }, 10000);

  // Expose functions globally
  window.lucideManager = {
    patchLucide,
    initIcons,
    createSpriteIcon
  };
})();
