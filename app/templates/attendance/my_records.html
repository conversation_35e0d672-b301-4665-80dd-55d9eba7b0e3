{% extends "base.html" %}
{% from "partials/pagination.html" import render_pagination %}
{% from "partials/forms/base_form.html" import form_group %}
{% from "components/button.html" import button_group, icon_button %}
{% from "components/simple_table.html" import simple_table %}
{% from "components/page_header.html" import page_header %}
{% from "components/table_header.html" import table_header %}
{% from "components/modal.html" import modal %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<!-- Hidden fields for JavaScript -->
<input type="hidden" id="attendance-form-url" data-url="{{ url_for('attendance.get_attendance_form') }}">
<input type="hidden" id="csrf-token" data-token="{{ csrf_token() }}">

<div class="space-y-6">
  <!-- Page Header -->
  {{ page_header(
    title=title,
    button_text="Request Attendance",
    button_icon="plus",
    button_action="openAttendanceRequestForm()",
    description="View and manage your attendance records"
  ) }}

  <!-- Quick Actions -->
  {% set quick_action_buttons = [
    {"text": "Calendar View", "variant": "outline", "href": url_for('attendance.attendance_calendar'), "icon": "calendar"}
  ] %}
  {{ button_group(quick_action_buttons) }}

  <!-- Filter Section -->
  <div class="card">
    <div class="card-header">
      <h3 class="card-title">Filter Attendance Records</h3>
    </div>
    <div class="card-content">
      <form method="GET" class="space-y-4">
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <!-- Attendance Type Filter -->
          {{ form_group(
            label="Attendance Type",
            name="attendance_type",
            type="select",
            value=request.args.get('attendance_type', ''),
            options=filter_form.attendance_type.choices
          ) }}

          <!-- Status Filter -->
          {{ form_group(
            label="Status",
            name="status",
            type="select",
            value=request.args.get('status', ''),
            options=filter_form.status.choices
          ) }}

          <!-- Start Date Filter -->
          {{ form_group(
            label="Start Date",
            name="start_date",
            type="date",
            value=request.args.get('start_date', '')
          ) }}

          <!-- End Date Filter -->
          {{ form_group(
            label="End Date",
            name="end_date",
            type="date",
            value=request.args.get('end_date', '')
          ) }}
        </div>

        <!-- Filter Actions -->
        <div class="flex justify-end gap-2">
          {% set filter_buttons = [
            {"text": "Reset", "variant": "outline", "href": url_for('attendance.my_attendance'), "icon": "rotate-ccw"},
            {"text": "Apply Filters", "variant": "primary", "type": "submit", "icon": "filter"}
          ] %}
          {{ button_group(filter_buttons) }}
        </div>
      </form>
    </div>
  </div>

  <!-- Attendance Records Table -->
  <div class="card">
    {{ table_header(
      title="My Attendance Records",
      count=attendance_records|length,
      count_label="records"
    ) }}

    {% call simple_table(
      headers=[
        {'label': 'Date', 'sortable': True},
        {'label': 'Type'},
        {'label': 'Status'},
        {'label': 'Actions', 'align': 'right'}
      ],
      items=attendance_records,
      empty_icon="calendar-x",
      empty_title="No attendance records found",
      empty_description="Create your first attendance request to get started.",
      empty_button_text="Request Attendance",
      empty_button_icon="plus",
      empty_button_action="openAttendanceRequestForm()"
    ) %}
      {% for record in attendance_records %}
      <tr class="border-b border-border hover:bg-muted/30">
        <td class="px-4 py-3 text-sm font-medium">
          <div class="flex items-center">
            <i data-lucide="calendar" class="w-4 h-4 mr-2 text-muted-foreground"></i>
            <div>
              <div>{{ record.date.strftime('%Y-%m-%d') }}</div>
              <div class="text-xs text-muted-foreground">{{ record.date.strftime('%A') }}</div>
            </div>
          </div>
        </td>
        <td class="px-4 py-3 text-sm">
          <div class="font-medium">{{ record.attendance_type.name }}</div>
          <div class="text-xs text-muted-foreground">{{ record.attendance_type.code }}</div>
        </td>
        <td class="px-4 py-3 text-xs">
          {% if record.status == 'Pending' %}
            <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200">
              <i data-lucide="clock" class="w-3 h-3 mr-1"></i>
              Pending
            </span>
          {% elif record.status == 'Approved' %}
            <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
              <i data-lucide="check" class="w-3 h-3 mr-1"></i>
              Approved
            </span>
          {% elif record.status == 'Rejected' %}
            <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200">
              <i data-lucide="x" class="w-3 h-3 mr-1"></i>
              Rejected
            </span>
          {% elif record.status == 'Auto-Approved' %}
            <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200">
              <i data-lucide="zap" class="w-3 h-3 mr-1"></i>
              Auto
            </span>
          {% elif record.status == 'Cancelled' %}
            <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200">
              <i data-lucide="x-circle" class="w-3 h-3 mr-1"></i>
              Cancelled
            </span>
          {% else %}
            <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200">
              {{ record.status }}
            </span>
          {% endif %}
        </td>
        <td class="px-4 py-3 text-right">
          <div class="flex justify-end space-x-2">
            {{ icon_button("eye", variant="ghost", size="sm", onclick="viewRecord(" ~ record.id ~ ")", title="View Details") }}
            {% if record.status == 'Pending' %}
              {{ icon_button("edit", variant="ghost", size="sm", onclick="editRecord(" ~ record.id ~ ")", title="Edit Record") }}
              {{ icon_button("trash-2", variant="ghost", size="sm", onclick="deleteRecord(" ~ record.id ~ ", '" ~ record.date.strftime('%Y-%m-%d') ~ "')", title="Delete Record", class_extra="text-destructive hover:text-destructive") }}
            {% endif %}
          </div>
        </td>
      </tr>
      {% endfor %}
    {% endcall %}

    <!-- Pagination -->
    {% include "partials/pagination.html" %}
  </div>
</div>

<!-- Record Details Modal -->
{% call modal(
  id='record-details',
  title='Attendance Record Details',
  description='View complete information about this attendance record',
  size='lg',
  show_footer=false
) %}
  <div id="record-details-content" class="space-y-6">
    <!-- Loading state -->
    <div class="text-center py-8">
      <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
      <p class="mt-2 text-sm text-muted-foreground">Loading record details...</p>
    </div>
  </div>
{% endcall %}

<script>
// Register attendance form with drawer manager
function registerAttendanceForm() {
  if (!window.drawerManager) {
    console.error('DrawerManager not found');
    return;
  }

  window.drawerManager.registerFormType('attendance_request', {
    createUrl: '/attendance/form',
    editUrl: '/attendance/edit-form/{id}',
    size: 'md',
    position: 'right'
  });
}

// Open attendance request form
function openAttendanceRequestForm() {
  if (window.drawerManager) {
    window.drawerManager.openForm('attendance_request');
  } else {
    // Fallback to regular page
    window.location.href = '{{ url_for("attendance.request_attendance") }}';
  }
}

// View record details function
function viewRecord(recordId) {
  if (typeof showModal !== 'function') {
    console.error('Modal system not available');
    return;
  }

  // Show modal with loading state
  const template = document.getElementById('record-details-template');
  if (!template) {
    console.error('Record details template not found');
    return;
  }

  showModal({
    id: 'record-details',
    content: template.innerHTML,
    size: 'lg'
  });

  // Fetch record details
  fetch(`/attendance/record-details/${recordId}`)
    .then(response => response.json())
    .then(data => {
      if (data.success) {
        updateRecordDetailsModal(data.record);
      } else {
        showRecordDetailsError(data.message || 'Failed to load record details');
      }
    })
    .catch(error => {
      console.error('Error fetching record details:', error);
      showRecordDetailsError('An error occurred while loading record details');
    });
}

// Update record details modal content
function updateRecordDetailsModal(record) {
  const content = document.getElementById('record-details-content');
  if (!content) return;

  const statusBadgeClass = getStatusBadgeClass(record.status);
  const durationInfo = getDurationInfo(record);
  const holidayInfo = getHolidayInfo(record);

  content.innerHTML = `
    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
      <!-- Basic Information -->
      <div class="space-y-4">
        <h3 class="text-lg font-semibold text-foreground border-b border-border pb-2">Basic Information</h3>

        <div class="space-y-3">
          <div class="flex justify-between items-start">
            <span class="text-sm font-medium text-muted-foreground">Date:</span>
            <div class="text-right">
              <div class="text-sm font-medium">${record.date}</div>
              <div class="text-xs text-muted-foreground">${record.day_of_week}</div>
            </div>
          </div>

          <div class="flex justify-between items-start">
            <span class="text-sm font-medium text-muted-foreground">Type:</span>
            <div class="text-right">
              <div class="text-sm font-medium">${record.attendance_type.name}</div>
              <div class="text-xs text-muted-foreground">${record.attendance_type.code}</div>
            </div>
          </div>

          <div class="flex justify-between items-center">
            <span class="text-sm font-medium text-muted-foreground">Status:</span>
            <span class="${statusBadgeClass}">${record.status}</span>
          </div>

          ${durationInfo}
        </div>
      </div>

      <!-- Additional Details -->
      <div class="space-y-4">
        <h3 class="text-lg font-semibold text-foreground border-b border-border pb-2">Additional Details</h3>

        <div class="space-y-3">
          ${holidayInfo}

          <div class="flex justify-between items-start">
            <span class="text-sm font-medium text-muted-foreground">Notes:</span>
            <div class="text-right max-w-xs">
              <div class="text-sm ${record.notes ? '' : 'text-muted-foreground italic'}">${record.notes || 'No notes provided'}</div>
            </div>
          </div>

          <div class="flex justify-between items-start">
            <span class="text-sm font-medium text-muted-foreground">Created:</span>
            <div class="text-right">
              <div class="text-sm">${record.created_at}</div>
            </div>
          </div>

          ${record.updated_at ? `
          <div class="flex justify-between items-start">
            <span class="text-sm font-medium text-muted-foreground">Last Updated:</span>
            <div class="text-right">
              <div class="text-sm">${record.updated_at}</div>
            </div>
          </div>
          ` : ''}
        </div>
      </div>
    </div>

    ${record.approval_notes ? `
    <div class="mt-6 p-4 bg-muted/30 rounded-lg border border-border">
      <h4 class="text-sm font-semibold text-foreground mb-2">Approval Notes</h4>
      <p class="text-sm text-muted-foreground">${record.approval_notes}</p>
    </div>
    ` : ''}
  `;
}

// Show error in record details modal
function showRecordDetailsError(message) {
  const content = document.getElementById('record-details-content');
  if (!content) return;

  content.innerHTML = `
    <div class="text-center py-8">
      <div class="w-12 h-12 mx-auto mb-4 text-destructive">
        <i data-lucide="alert-circle" class="w-12 h-12"></i>
      </div>
      <h3 class="text-lg font-semibold text-foreground mb-2">Error Loading Details</h3>
      <p class="text-sm text-muted-foreground">${message}</p>
    </div>
  `;

  // Re-initialize icons
  if (typeof lucide !== 'undefined' && lucide.createIcons) {
    lucide.createIcons({ root: content });
  }
}

// Helper functions
function getStatusBadgeClass(status) {
  const classes = {
    'Pending': 'inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200',
    'Approved': 'inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200',
    'Rejected': 'inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200',
    'Auto-Approved': 'inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200',
    'Cancelled': 'inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200'
  };
  return classes[status] || classes['Cancelled'];
}

function getDurationInfo(record) {
  if (record.attendance_type.is_full_day) {
    return `
      <div class="flex justify-between items-center">
        <span class="text-sm font-medium text-muted-foreground">Duration:</span>
        <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200">
          Full Day
        </span>
      </div>
    `;
  } else {
    let durationHtml = '';
    if (record.duration_hours) {
      durationHtml += `
        <div class="flex justify-between items-center">
          <span class="text-sm font-medium text-muted-foreground">Duration:</span>
          <span class="text-sm font-medium">${record.duration_hours}h</span>
        </div>
      `;
    }
    if (record.start_time && record.end_time) {
      durationHtml += `
        <div class="flex justify-between items-center">
          <span class="text-sm font-medium text-muted-foreground">Time:</span>
          <span class="text-sm">${record.start_time} - ${record.end_time}</span>
        </div>
      `;
    }
    return durationHtml;
  }
}

function getHolidayInfo(record) {
  if (record.is_holiday_work) {
    return `
      <div class="flex justify-between items-start">
        <span class="text-sm font-medium text-muted-foreground">Holiday Work:</span>
        <div class="text-right">
          <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-amber-100 text-amber-800 dark:bg-amber-900 dark:text-amber-200">
            <i data-lucide="sun" class="w-3 h-3 mr-1"></i>
            Yes
          </span>
          ${record.holiday_work_reason ? `<div class="text-xs text-muted-foreground mt-1 max-w-xs">${record.holiday_work_reason}</div>` : ''}
        </div>
      </div>
    `;
  } else {
    return `
      <div class="flex justify-between items-center">
        <span class="text-sm font-medium text-muted-foreground">Holiday Work:</span>
        <span class="text-sm text-muted-foreground">No</span>
      </div>
    `;
  }
}

// Edit record function
function editRecord(recordId) {
  // First check if record can be edited
  fetch(`/attendance/can-edit/${recordId}`)
    .then(response => response.json())
    .then(data => {
      if (data.can_edit) {
        // Open edit form in drawer
        if (window.drawerManager) {
          window.drawerManager.openForm('attendance_request', recordId);
        } else {
          // Fallback to regular page
          window.location.href = `{{ url_for('attendance.edit_attendance', record_id=0) }}`.replace('0', recordId);
        }
      } else {
        // Show cannot edit message
        if (typeof showModal === 'function') {
          showModal({
            id: 'cannot-edit-modal',
            content: `
              <div class="flex flex-col">
                <!-- Header -->
                <div class="flex items-start justify-between px-6 pt-6 pb-4">
                  <div class="flex flex-col space-y-1 text-left pr-6">
                    <h2 id="cannot-edit-modal-title" class="text-lg font-semibold leading-tight tracking-tight text-gray-900 dark:text-white">Cannot Edit Record</h2>
                    <p id="cannot-edit-modal-description" class="mt-1 text-sm text-gray-500 dark:text-gray-400">This attendance record cannot be modified</p>
                  </div>
                  <button type="button" class="absolute right-4 top-4 rounded-full opacity-70 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 h-8 w-8 inline-flex items-center justify-center transition-all duration-200 hover:opacity-100 disabled:pointer-events-none" data-modal-close aria-label="Close">
                    <i data-lucide="x" class="h-4 w-4 pointer-events-none"></i>
                    <span class="sr-only">Close</span>
                  </button>
                </div>
                <!-- Content -->
                <div class="px-6 pb-6 pt-2 overflow-y-auto">
                  <div class="bg-orange-50 dark:bg-orange-900/20 border border-orange-200 dark:border-orange-800 rounded-lg p-4">
                    <div class="flex items-start">
                      <div class="flex-shrink-0">
                        <i data-lucide="alert-triangle" class="h-5 w-5 text-orange-400"></i>
                      </div>
                      <div class="ml-3">
                        <h3 class="text-sm font-medium text-orange-800 dark:text-orange-200">
                          Record Cannot Be Edited
                        </h3>
                        <div class="mt-2 text-sm text-orange-700 dark:text-orange-300">
                          <p>${data.reason || 'Only pending attendance records can be edited.'}</p>
                          <p class="mt-2">Current status: <strong>${data.status || 'Unknown'}</strong></p>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            `,
            size: 'md'
          });
        } else {
          alert(data.reason || 'Only pending attendance records can be edited.');
        }
      }
    })
    .catch(error => {
      console.error('Error checking edit permission:', error);
      showToast('Error checking edit permission', 'error');
    });
}

// Delete record function
function deleteRecord(recordId, recordDate) {
  if (confirm(`Are you sure you want to delete your attendance request for ${recordDate}?`)) {
    fetch(`{{ url_for('attendance.delete_attendance', record_id=0) }}`.replace('0', recordId), {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
        'X-CSRFToken': '{{ csrf_token() }}'
      }
    })
    .then(response => response.json())
    .then(data => {
      if (data.success) {
        showToast(data.message, 'success');
        location.reload();
      } else {
        showToast(data.message, 'error');
      }
    })
    .catch(error => {
      showToast('An error occurred while deleting the record.', 'error');
    });
  }
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
  registerAttendanceForm();

  // Check if we should open the drawer automatically
  const urlParams = new URLSearchParams(window.location.search);
  if (urlParams.get('open_drawer') === 'true') {
    // Remove the parameter from URL
    const newUrl = window.location.pathname;
    window.history.replaceState({}, document.title, newUrl);

    // Open the attendance request form
    setTimeout(() => {
      openAttendanceRequestForm();
    }, 100);
  }
});
</script>
{% endblock %}
