#!/usr/bin/env python3
"""
Test script for bulk user import functionality.
"""

import os
import sys
import tempfile
from io import StringIO

# Add the app directory to the path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app import create_app, db
from app.models import User, EmployeeDetail, BusinessUnit, BusinessSegment
from app.services.user_import_service import UserImportService
from werkzeug.datastructures import FileStorage

def test_user_import():
    """Test the user import functionality."""
    app = create_app()
    
    with app.app_context():
        print("Testing User Import Service...")
        
        # Create test CSV content
        csv_content = """name,email,role,first_name,middle_name,last_name,legal_name,job_title,emp_type,enterprise_id,job_code,job_code_track_level,manager_level,business_unit_code,hire_date,EMP_STATUS
Test User 1,<EMAIL>,User,Test,Middle,User1,Test Middle User1,Software Engineer,Full-time,EMP001,SE01,L2,M1,OPS,2024-01-15,active
Test User 2,<EMAIL>,Manager,Test,Middle,User2,Test Middle User2,Engineering Manager,Full-time,EMP002,EM01,L4,M3,FIN,2023-06-01,active"""
        
        # Create a temporary file
        with tempfile.NamedTemporaryFile(mode='w', suffix='.csv', delete=False) as f:
            f.write(csv_content)
            temp_file_path = f.name
        
        try:
            # Create FileStorage object
            with open(temp_file_path, 'rb') as f:
                file_storage = FileStorage(
                    stream=f,
                    filename='test_users.csv',
                    content_type='text/csv'
                )
                
                # Initialize import service
                import_service = UserImportService()
                
                # Test the import
                result = import_service.import_users(
                    file=file_storage,
                    default_role='User',
                    handle_duplicates='skip',
                    default_password='TestPassword123!',
                    send_welcome_email=False
                )
                
                print(f"Import Result: {result}")
                
                if result['success']:
                    print("✅ Import successful!")
                    print(f"Created: {result['details']['created']}")
                    print(f"Updated: {result['details']['updated']}")
                    print(f"Skipped: {result['details']['skipped']}")
                    print(f"Errors: {result['details']['errors']}")
                    
                    # Check if users were created
                    test_user1 = User.query.filter_by(email='<EMAIL>').first()
                    test_user2 = User.query.filter_by(email='<EMAIL>').first()
                    
                    if test_user1:
                        print(f"✅ User 1 created: {test_user1.name} ({test_user1.email})")
                        if test_user1.employee_detail:
                            print(f"   Employee detail: {test_user1.employee_detail.first_name} {test_user1.employee_detail.last_name}")
                    
                    if test_user2:
                        print(f"✅ User 2 created: {test_user2.name} ({test_user2.email})")
                        if test_user2.employee_detail:
                            print(f"   Employee detail: {test_user2.employee_detail.first_name} {test_user2.employee_detail.last_name}")
                    
                else:
                    print("❌ Import failed!")
                    print(f"Error: {result['message']}")
                    if result['details']['error_messages']:
                        for error in result['details']['error_messages']:
                            print(f"  - {error}")
                
        finally:
            # Clean up
            os.unlink(temp_file_path)
            
            # Clean up test users
            test_users = User.query.filter(User.email.in_(['<EMAIL>', '<EMAIL>'])).all()
            for user in test_users:
                if user.employee_detail:
                    db.session.delete(user.employee_detail)
                db.session.delete(user)
            db.session.commit()
            print("🧹 Cleaned up test data")

def test_case_insensitive_columns():
    """Test case insensitive column handling."""
    app = create_app()
    
    with app.app_context():
        print("\nTesting Case Insensitive Columns...")
        
        # Create test CSV content with mixed case headers
        csv_content = """NAME,EMAIL,ROLE,FIRST_NAME,middle_name,Last_Name,EMP_TYPE,hire_date
Test Case User,<EMAIL>,User,Test,Case,User,Full-time,2024-01-15"""
        
        # Create a temporary file
        with tempfile.NamedTemporaryFile(mode='w', suffix='.csv', delete=False) as f:
            f.write(csv_content)
            temp_file_path = f.name
        
        try:
            # Create FileStorage object
            with open(temp_file_path, 'rb') as f:
                file_storage = FileStorage(
                    stream=f,
                    filename='test_case_users.csv',
                    content_type='text/csv'
                )
                
                # Initialize import service
                import_service = UserImportService()
                
                # Test the import
                result = import_service.import_users(
                    file=file_storage,
                    default_role='User',
                    handle_duplicates='skip',
                    default_password='TestPassword123!',
                    send_welcome_email=False
                )
                
                print(f"Case Insensitive Test Result: {result}")
                
                if result['success']:
                    print("✅ Case insensitive import successful!")
                    
                    # Check if user was created
                    test_user = User.query.filter_by(email='<EMAIL>').first()
                    
                    if test_user:
                        print(f"✅ User created: {test_user.name} ({test_user.email})")
                        if test_user.employee_detail:
                            print(f"   Employee detail: {test_user.employee_detail.first_name} {test_user.employee_detail.last_name}")
                            print(f"   Employee type: {test_user.employee_detail.emp_type}")
                    
                else:
                    print("❌ Case insensitive import failed!")
                    print(f"Error: {result['message']}")
                
        finally:
            # Clean up
            os.unlink(temp_file_path)
            
            # Clean up test user
            test_user = User.query.filter_by(email='<EMAIL>').first()
            if test_user:
                if test_user.employee_detail:
                    db.session.delete(test_user.employee_detail)
                db.session.delete(test_user)
                db.session.commit()
            print("🧹 Cleaned up test data")

if __name__ == '__main__':
    test_user_import()
    test_case_insensitive_columns()
    print("\n✅ All tests completed!")
