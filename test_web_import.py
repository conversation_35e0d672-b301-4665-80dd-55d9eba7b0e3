#!/usr/bin/env python3
"""
Test script for web-based bulk user import functionality.
"""

import os
import sys
import tempfile
import requests

# Add the app directory to the path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_web_import():
    """Test the web-based import functionality."""

    # Create a session
    session = requests.Session()

    base_url = "http://127.0.0.1:5003"

    try:
        print("Testing Web-based Import...")

        # Step 1: Get the login page to establish session
        print("1. Getting login page...")
        login_page = session.get(f"{base_url}/auth/login")
        print(f"   Login page status: {login_page.status_code}")

        if login_page.status_code != 200:
            print("❌ Cannot access login page")
            return

        # Step 2: Try to access the bulk import form (should redirect to login)
        print("2. Testing bulk import form access (should redirect)...")
        form_response = session.get(f"{base_url}/admin/users/bulk-import/form")
        print(f"   Form access status: {form_response.status_code}")
        print(f"   Final URL: {form_response.url}")

        if "login" in form_response.url:
            print("✅ Correctly redirected to login (authentication required)")
        else:
            print("⚠️  No redirect to login - check authentication")

        # Step 3: Check if we can access the admin area at all
        print("3. Testing admin area access...")
        admin_response = session.get(f"{base_url}/admin/")
        print(f"   Admin area status: {admin_response.status_code}")
        print(f"   Final URL: {admin_response.url}")

        # Step 4: Test the POST endpoint (should return 400 for missing CSRF)
        print("4. Testing POST endpoint (should return 400 for CSRF)...")
        post_response = session.post(f"{base_url}/admin/users/drawer/bulk-import")
        print(f"   POST status: {post_response.status_code}")

        if post_response.status_code == 400 and "CSRF" in post_response.text:
            print("✅ POST endpoint correctly requires CSRF token")
        elif post_response.status_code == 302:
            print("✅ POST endpoint correctly redirects unauthenticated users")
        else:
            print(f"⚠️  Unexpected POST response: {post_response.status_code}")

        print("\n📋 Summary:")
        print("- The import functionality is properly protected by authentication")
        print("- You need to log in as an admin user to access the import feature")
        print("- The routes are working correctly")
        print("- The 'Method Not Allowed' error you're seeing is likely due to:")
        print("  1. Not being logged in as an admin user")
        print("  2. CSRF token issues")
        print("  3. Form submission problems")

        print("\n🔧 To test the import:")
        print("1. Make sure you're logged in as an admin user")
        print("2. Go to Admin → Users")
        print("3. Click 'Import Users' button")
        print("4. Upload the sample_users.csv file")
        print("5. Configure the import settings")
        print("6. Click 'Import Users'")

    except requests.exceptions.ConnectionError:
        print("❌ Cannot connect to the server. Make sure it's running on port 5003")
    except Exception as e:
        print(f"❌ Error during testing: {e}")

def create_test_admin_user():
    """Create a test admin user for testing."""
    from app import create_app, db
    from app.models import User

    app = create_app()
    with app.app_context():
        # Check if test admin exists
        test_admin = User.query.filter_by(email='<EMAIL>').first()

        if not test_admin:
            print("Creating test admin user...")
            test_admin = User(
                name='Test Admin',  # type: ignore
                email='<EMAIL>',  # type: ignore
                role='Admin'  # type: ignore
            )
            test_admin.set_password('Admin123!')
            test_admin.is_active = True

            db.session.add(test_admin)
            db.session.commit()

            print("✅ Test admin user created:")
            print("   Email: <EMAIL>")
            print("   Password: Admin123!")
        else:
            print("✅ Test admin user already exists:")
            print("   Email: <EMAIL>")
            print("   Password: Admin123!")

if __name__ == '__main__':
    create_test_admin_user()
    print()
    test_web_import()
