"""
Holiday seeder for populating the database with common holidays.
"""

from datetime import date
from app import db
from app.models.attendance import Holiday
from .db_utils import get_or_create


def seed_holidays():
    """Seeds the database with common holidays for different regions."""

    # Get holidays data for 2025
    all_holidays = get_2025_holidays()
    print(f"Seeding {len(all_holidays)} holidays for 2025")

    created_count = 0
    updated_count = 0
    skipped_count = 0

    for holiday_data in all_holidays:
        try:
            # Check if holiday already exists for this date and region
            existing_holiday = Holiday.query.filter(
                Holiday.date == holiday_data['date'],
                Holiday.region_code == holiday_data['region_code']
            ).first()

            if existing_holiday:
                # Update existing holiday if data is different
                updated = False
                if existing_holiday.name != holiday_data['name']:
                    existing_holiday.name = holiday_data['name']
                    updated = True
                if existing_holiday.description != holiday_data['description']:
                    existing_holiday.description = holiday_data['description']
                    updated = True
                if existing_holiday.holiday_type != holiday_data.get('holiday_type', 'regular'):
                    existing_holiday.holiday_type = holiday_data.get('holiday_type', 'regular')
                    updated = True

                if updated:
                    updated_count += 1
                    print(f"Updated holiday: {holiday_data['name']} ({holiday_data['region_code']}) on {holiday_data['date']}")
                else:
                    skipped_count += 1
            else:
                # Create new holiday
                holiday = Holiday(
                    name=holiday_data['name'],
                    date=holiday_data['date'],
                    region_code=holiday_data['region_code'],
                    description=holiday_data['description'],
                    holiday_type=holiday_data.get('holiday_type', 'regular')
                )
                db.session.add(holiday)
                created_count += 1
                print(f"Created holiday: {holiday_data['name']} ({holiday_data['region_code']}) on {holiday_data['date']}")

        except Exception as e:
            print(f"Error processing holiday {holiday_data['name']}: {str(e)}")
            continue

    try:
        db.session.commit()
        print(f"\nHoliday seeding completed!")
        print(f"Created: {created_count} holidays")
        print(f"Updated: {updated_count} holidays")
        print(f"Skipped: {skipped_count} holidays")
        print(f"Total processed: {len(all_holidays)} holidays")

        return {
            'created': created_count,
            'updated': updated_count,
            'skipped': skipped_count,
            'total': len(all_holidays)
        }

    except Exception as e:
        db.session.rollback()
        print(f"Error committing holiday data: {str(e)}")
        return {
            'created': 0,
            'updated': 0,
            'skipped': 0,
            'total': 0,
            'error': str(e)
        }


def get_2025_holidays():
    """Get holidays data for 2025."""
    # US Holidays (2025 data)
    us_holidays = [
        {
            'name': "New Year's Day",
            'date': date(2025, 1, 1),
            'region_code': 'US',
            'holiday_type': 'federal',
            'description': 'First day of the year'
        },
        {
            'name': "Martin Luther King Jr. Day",
            'date': date(2025, 1, 20),
            'region_code': 'US',
            'holiday_type': 'federal',
            'description': 'Honors civil rights leader Martin Luther King Jr.'
        },
        {
            'name': "Inauguration Day",
            'date': date(2025, 1, 20),
            'region_code': 'US',
            'holiday_type': 'federal',
            'description': 'Presidential inauguration ceremony (every 4 years)'
        },
        {
            'name': "Presidents' Day",
            'date': date(2025, 2, 17),
            'region_code': 'US',
            'holiday_type': 'federal',
            'description': 'Honors all U.S. presidents'
        },
        {
            'name': "Memorial Day",
            'date': date(2025, 5, 26),
            'region_code': 'US',
            'holiday_type': 'federal',
            'description': 'Honors military personnel who died in service'
        },
        {
            'name': "Juneteenth National Independence Day",
            'date': date(2025, 6, 19),
            'region_code': 'US',
            'holiday_type': 'federal',
            'description': 'Commemorates the end of slavery in the U.S.'
        },
        {
            'name': "Independence Day",
            'date': date(2025, 7, 4),
            'region_code': 'US',
            'holiday_type': 'federal',
            'description': 'Celebrates the Declaration of Independence'
        },
        {
            'name': "Labor Day",
            'date': date(2025, 9, 1),
            'region_code': 'US',
            'holiday_type': 'federal',
            'description': 'Celebrates the American labor movement'
        },
        {
            'name': "Columbus Day",
            'date': date(2025, 10, 13),
            'region_code': 'US',
            'holiday_type': 'federal',
            'description': 'Commemorates Christopher Columbus\'s landing in the Americas'
        },
        {
            'name': "Veterans Day",
            'date': date(2025, 11, 11),
            'region_code': 'US',
            'holiday_type': 'federal',
            'description': 'Honors military veterans'
        },
        {
            'name': "Thanksgiving Day",
            'date': date(2025, 11, 27),
            'region_code': 'US',
            'holiday_type': 'federal',
            'description': 'Day of giving thanks'
        }
    ]

    # Philippines Holidays (2025 data)
    ph_holidays = [
        {
            'name': "Chinese New Year",
            'date': date(2025, 1, 29),
            'region_code': 'PH',
            'holiday_type': 'special-non-working',
            'description': 'Celebrates the start of the Chinese lunar calendar'
        },
        {
            'name': "EDSA People Power Revolution Anniversary",
            'date': date(2025, 2, 25),
            'region_code': 'PH',
            'holiday_type': 'special-working',
            'description': 'Commemorates the 1986 EDSA Revolution'
        },
        {
            'name': "Eid'l Fitr",
            'date': date(2025, 4, 1),
            'region_code': 'PH',
            'holiday_type': 'regular',
            'description': 'Feast of Ramadhan'
        },
        {
            'name': "Araw ng Kagitingan",
            'date': date(2025, 4, 9),
            'region_code': 'PH',
            'holiday_type': 'regular',
            'description': 'Day of Valor'
        },
        {
            'name': "National and Local Elections",
            'date': date(2025, 4, 12),
            'region_code': 'PH',
            'holiday_type': 'special-non-working',
            'description': 'National Elections Observances'
        },
        {
            'name': "Maundy Thursday",
            'date': date(2025, 4, 17),
            'region_code': 'PH',
            'holiday_type': 'regular',
            'description': 'Commemoration of the Last Supper'
        },
        {
            'name': "Good Friday",
            'date': date(2025, 4, 18),
            'region_code': 'PH',
            'holiday_type': 'regular',
            'description': 'Commemoration of the crucifixion of Jesus Christ'
        },
        {
            'name': "Black Saturday",
            'date': date(2025, 4, 19),
            'region_code': 'PH',
            'holiday_type': 'special-non-working',
            'description': 'Observance of Jesus Christ\'s burial'
        },
        {
            'name': "Labor Day",
            'date': date(2025, 5, 1),
            'region_code': 'PH',
            'holiday_type': 'regular',
            'description': 'Araw ng Paggawa'
        },
        {
            'name': "Eidul Adha",
            'date': date(2025, 6, 6),
            'region_code': 'PH',
            'holiday_type': 'regular',
            'description': 'Feast of Sacrifice'
        },
        {
            'name': "Independence Day",
            'date': date(2025, 6, 12),
            'region_code': 'PH',
            'holiday_type': 'regular',
            'description': 'Araw ng Kalayaan'
        },
        {
            'name': "Ninoy Aquino Day",
            'date': date(2025, 8, 21),
            'region_code': 'PH',
            'holiday_type': 'special-non-working',
            'description': 'Commemorates the assassination of Benigno Aquino Jr.'
        },
        {
            'name': "National Heroes Day",
            'date': date(2025, 8, 25),
            'region_code': 'PH',
            'holiday_type': 'regular',
            'description': 'Araw ng mga Bayani'
        },
        {
            'name': "All Saints' Day Eve",
            'date': date(2025, 10, 31),
            'region_code': 'PH',
            'holiday_type': 'special-non-working',
            'description': 'All Hallows\' Eve'
        },
        {
            'name': "All Saints' Day",
            'date': date(2025, 11, 1),
            'region_code': 'PH',
            'holiday_type': 'special-non-working',
            'description': 'Honors all saints'
        },
        {
            'name': "Bonifacio Day",
            'date': date(2025, 11, 30),
            'region_code': 'PH',
            'holiday_type': 'regular',
            'description': 'Commemorating Andres Bonifacio'
        },
        {
            'name': "Feast of the Immaculate Conception",
            'date': date(2025, 12, 8),
            'region_code': 'PH',
            'holiday_type': 'special-non-working',
            'description': 'Celebrates the conception of the Virgin Mary'
        },
        {
            'name': "Rizal Day",
            'date': date(2025, 12, 30),
            'region_code': 'PH',
            'holiday_type': 'regular',
            'description': 'Araw ni Rizal'
        },
        {
            'name': "New Year's Eve",
            'date': date(2025, 12, 31),
            'region_code': 'PH',
            'holiday_type': 'special-non-working',
            'description': 'Huling Araw ng Taon'
        }
    ]

    # Global Holidays (observed worldwide) - 2025 data
    global_holidays = [
        {
            'name': "New Year's Day",
            'date': date(2025, 1, 1),
            'region_code': 'GLOBAL',
            'holiday_type': 'regular',
            'description': 'First day of the year'
        },
        {
            'name': "Christmas Day",
            'date': date(2025, 12, 25),
            'region_code': 'GLOBAL',
            'holiday_type': 'regular',
            'description': 'Celebration of the birth of Jesus Christ'
        }
    ]

    return us_holidays + ph_holidays + global_holidays


if __name__ == "__main__":
    import os
    import sys
    # Add the parent directory to the Python path
    sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))
    from app import create_app
    app = create_app()
    with app.app_context():
        seed_holidays()
