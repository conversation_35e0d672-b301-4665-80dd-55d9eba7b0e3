{% extends "base.html" %}

{% block title %}Navigation Test{% endblock %}
{% block header %}Navigation Test{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-8">
  <div class="bg-card rounded-lg shadow-sm p-6 mb-6">
    <h1 class="text-2xl font-bold mb-4">3-Second Navigation Loader Test</h1>
    <p class="mb-6">Click any link below to test the automatic 3-second page loader. The loader should appear immediately and remain visible for exactly 3 seconds.</p>

    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
      <!-- Test Navigation Links -->
      <div class="bg-muted/30 p-4 rounded-md">
        <h3 class="text-lg font-semibold mb-2">Dashboard Links</h3>
        <div class="space-y-2">
          <a href="/admin/dashboard" class="btn btn-primary block text-center">Admin Dashboard</a>
          <a href="/admin/users" class="btn btn-outline block text-center">Users Page</a>
          <a href="/admin/settings" class="btn btn-outline block text-center">Settings Page</a>
        </div>
      </div>

      <!-- Business Links -->
      <div class="bg-muted/30 p-4 rounded-md">
        <h3 class="text-lg font-semibold mb-2">Business Pages</h3>
        <div class="space-y-2">
          <a href="/admin/business-units" class="btn btn-primary block text-center">Business Units</a>
          <a href="/admin/business-segments" class="btn btn-outline block text-center">Business Segments</a>
          <a href="/admin/employee-details" class="btn btn-outline block text-center">Employee Details</a>
        </div>
      </div>

      <!-- Special Cases -->
      <div class="bg-muted/30 p-4 rounded-md">
        <h3 class="text-lg font-semibold mb-2">Special Cases</h3>
        <div class="space-y-2">
          <a href="/admin/navigation-test" class="btn btn-success block text-center">Reload This Page</a>
          <a href="/admin/navigation-test" data-no-loader class="btn btn-secondary block text-center">No Loader Link</a>
          <a href="#section" class="btn btn-outline block text-center">Hash Link (No Loader)</a>
        </div>
      </div>
    </div>

    <!-- Test Results Section -->
    <div class="mt-8 p-4 bg-info/10 rounded-lg border border-info/20">
      <h3 class="text-lg font-semibold mb-2 flex items-center">
        <i data-lucide="info" class="w-5 h-5 mr-2"></i>
        Expected Behavior
      </h3>
      <ul class="list-disc pl-6 space-y-1 text-sm">
        <li><strong>Regular Links:</strong> Show loader for exactly 3 seconds</li>
        <li><strong>data-no-loader Links:</strong> Navigate without showing loader</li>
        <li><strong>Hash Links (#):</strong> No loader (same page navigation)</li>
        <li><strong>External Links:</strong> No loader (if any were present)</li>
      </ul>
    </div>

    <!-- Manual Test Section -->
    <div class="mt-6">
      <h3 class="text-lg font-semibold mb-4">Manual Loader Tests</h3>
      <div class="flex flex-wrap gap-2">
        <button onclick="showPageLoader('Manual test loader...')" class="btn btn-primary">
          Show Manual Loader
        </button>
        <button onclick="hidePageLoader()" class="btn btn-outline">
          Hide Loader
        </button>
        <button onclick="testTimedLoader()" class="btn btn-success">
          Test 3-Second Timed Loader
        </button>
      </div>
    </div>

    <!-- Back to Demo -->
    <div class="mt-8 pt-4 border-t border-border">
      <a href="/admin/page-loader-demo" class="btn btn-outline">
        <i data-lucide="arrow-left" class="w-4 h-4 mr-2"></i>
        Back to Page Loader Demo
      </a>
    </div>
  </div>
</div>

<div id="section" class="mt-8">
  <div class="bg-card rounded-lg shadow-sm p-6">
    <h2 class="text-xl font-bold mb-2">Hash Link Target Section</h2>
    <p>This section is the target for the hash link test above.</p>
  </div>
</div>
{% endblock %}

{% block scripts %}
<script>
document.addEventListener('DOMContentLoaded', function() {
  // Manual test for timed loader
  window.testTimedLoader = function() {
    showPageLoader('Testing 3-second automatic timing...');

    // The loader should automatically hide after 3 seconds due to minimumDuration
    setTimeout(() => {
      if (isPageLoaderActive()) {
        console.log('Loader still active as expected');
      } else {
        console.log('Loader hidden earlier than expected');
      }
    }, 2500);

    setTimeout(() => {
      if (!isPageLoaderActive()) {
        console.log('Loader properly hidden after 3 seconds');
        if (typeof showToast === 'function') {
          showToast('3-second timing test completed successfully!', { type: 'success' });
        }
      } else {
        console.log('Loader still active after 3 seconds - this might indicate an issue');
      }
    }, 3500);
  };
});
</script>
{% endblock %}
