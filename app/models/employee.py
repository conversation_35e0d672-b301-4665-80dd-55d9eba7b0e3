"""
Employee model and related functionality.
"""

from datetime import datetime
from typing import Dict, Any, List

from app import db
from app.models import PH_TZ

# Association table for many-to-many relationship between Team and EmployeeDetail
team_members = db.<PERSON>('team_members',
    db.<PERSON>umn('team_id', db.<PERSON>teger, db.<PERSON>('teams.id', name='fk_team_members_team_id'), primary_key=True),
    db.<PERSON>('employee_detail_id', db.Integer, db.<PERSON>('employee_details.id', name='fk_team_members_employee_detail_id'), primary_key=True),
    db.<PERSON>umn('created_at', db.DateTime, default=lambda: datetime.now(PH_TZ))
)


class EmployeeDetail(db.Model):
    """Employee-specific details model, linked to User."""
    __tablename__ = 'employee_details'

    # Employment status options
    STATUS_ACTIVE = 'active'
    STATUS_TERMINATED = 'terminated'
    STATUS_LEAVE = 'leave_of_absence'

    STATUS_CHOICES = [
        (STATUS_ACTIVE, 'Active'),
        (STATUS_TERMINATED, 'Terminated'),
        (STATUS_LEAVE, 'Leave of Absence')
    ]

    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('users.id', name='fk_employee_detail_user_id_cascade', ondelete='CASCADE'), nullable=False, unique=True)
    employee_number = db.Column(db.String(100), unique=True, nullable=True)
    first_name = db.Column(db.String(100), nullable=True)
    middle_name = db.Column(db.String(100), nullable=True)
    last_name = db.Column(db.String(100), nullable=True)
    legal_name = db.Column(db.String(200), nullable=True)
    job_title = db.Column(db.String(100), nullable=True)  # Renamed from position
    # department field removed - using business unit and segment instead
    phone = db.Column(db.String(20), nullable=True)
    emp_type = db.Column(db.String(50), nullable=True)  # e.g., Full-time, Part-time, Contract
    enterprise_id = db.Column(db.String(100), nullable=True)
    manager_name = db.Column(db.String(100), nullable=True)
    job_code = db.Column(db.String(50), nullable=True)
    manager_level = db.Column(db.String(50), nullable=True)
    job_code_track_level = db.Column(db.String(50), nullable=True)
    business_unit_id = db.Column(db.Integer, db.ForeignKey('business_units.id', name='fk_employee_detail_business_unit_id'), nullable=True)
    business_segment_id = db.Column(db.Integer, db.ForeignKey('business_segments.id', name='fk_employee_detail_business_segment_id'), nullable=True)
    hire_date = db.Column(db.Date, nullable=True)
    emp_status = db.Column(db.String(20), default=STATUS_ACTIVE, nullable=False)
    created_at = db.Column(db.DateTime, default=lambda: datetime.now(PH_TZ))
    updated_at = db.Column(db.DateTime, default=lambda: datetime.now(PH_TZ), onupdate=lambda: datetime.now(PH_TZ))
    direct_manager_user_id = db.Column(db.Integer, db.ForeignKey('users.id', name='fk_employee_detail_manager_user_id'), nullable=True)

    # Relationship with teams
    teams = db.relationship('Team', secondary=team_members, back_populates='members')

    # Relationship to direct manager (User) with explicit join condition
    direct_manager = db.relationship('User',
                                   foreign_keys=[direct_manager_user_id],
                                   primaryjoin="EmployeeDetail.direct_manager_user_id == User.id",
                                   back_populates="managed_employees")

    # Relationship to User for user_id with explicit foreign_keys and join condition
    user = db.relationship('User',
                          foreign_keys=[user_id],
                          primaryjoin="EmployeeDetail.user_id == User.id",
                          back_populates="employee_detail")

    __table_args__ = (
        db.Index('idx_employee_detail_business_unit', business_unit_id),
        db.Index('idx_employee_detail_manager_user_id', direct_manager_user_id),
        db.Index('idx_employee_detail_business_segment', business_segment_id),
    )

    # Relationships
    business_unit = db.relationship('BusinessUnit', backref='employee_details')
    business_segment = db.relationship('BusinessSegment', backref='employee_details')

    def __repr__(self) -> str:
        return f'<EmployeeDetail for {self.user.name if self.user else "Unknown"}>'

    @property
    def full_name(self) -> str:
        """Return the employee's full name (first_name + last_name), excluding middle_name."""
        names = [self.first_name, self.last_name]
        return ' '.join(name for name in names if name and name.strip())

    def to_dict(self) -> Dict[str, Any]:
        """Serialize employee details to dictionary."""
        return {
            'id': self.id,
            'user_id': self.user_id,
            'employee_number': self.employee_number,
            'first_name': self.first_name,
            'middle_name': self.middle_name,
            'last_name': self.last_name,
            'legal_name': self.legal_name,
            'job_title': self.job_title,
            'position': self.job_title,  # Alias for backward compatibility
            'phone': self.phone,
            'emp_type': self.emp_type,
            'enterprise_id': self.enterprise_id,
            'manager_name': self.manager_name,
            'job_code': self.job_code,
            'manager_level': self.manager_level,
            'job_code_track_level': self.job_code_track_level,
            'business_unit_id': self.business_unit_id,
            'business_segment_id': self.business_segment_id,
            'business_unit': self.business_unit.name if self.business_unit else None,
            'business_segment': self.business_segment.name if self.business_segment else None,
            'hire_date': self.hire_date.isoformat() if self.hire_date else None,
            'emp_status': self.emp_status,
            'bio': self.user.bio if self.user else None,
            'avatar': self.user.avatar if self.user else None,
            'teams': [team.name for team in self.teams] if self.teams else [],
            'direct_manager_user_id': self.direct_manager_user_id,
            'direct_manager_name': self.direct_manager.name if self.direct_manager else None,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None,
        }
