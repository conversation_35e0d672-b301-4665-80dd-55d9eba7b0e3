{# This form partial is loaded into a drawer by client-side JavaScript #}
{# The `action_url` and `form_title` are passed from the route #}
{% from "partials/forms/base_form.html" import form_group, form_actions, form_header %}

<form method="POST" action="{{ action_url }}" id="bulkImportDrawerForm" enctype="multipart/form-data" class="space-y-4">
  {{ form.hidden_tag() }}

  {{ form_header(
    title = form_title,
    description = "Upload a CSV or XLSX file to import multiple users at once"
  ) }}

  <div class="grid grid-cols-1 gap-4">
    {{ form_group(
      label = form.file.label.text,
      name = form.file.name,
      type = "file",
      required = form.file.flags.required,
      hint = "Supported formats: CSV, XLSX"
    ) }}
    {% if form.file.errors %}
      <div class="text-xs text-destructive mt-1">
        <ul>
          {% for error in form.file.errors %}<li>{{ error }}</li>{% endfor %}
        </ul>
      </div>
    {% endif %}

    {{ form_group(
      label = form.default_role.label.text,
      name = form.default_role.name,
      type = "select",
      value = form.default_role.data if form.default_role.data else "",
      required = form.default_role.flags.required,
      options = form.default_role.choices,
      hint = form.default_role.description
    ) }}
    {% if form.default_role.errors %}
      <div class="text-xs text-destructive mt-1">
        <ul>
          {% for error in form.default_role.errors %}<li>{{ error }}</li>{% endfor %}
        </ul>
      </div>
    {% endif %}

    {{ form_group(
      label = form.handle_duplicates.label.text,
      name = form.handle_duplicates.name,
      type = "select",
      value = form.handle_duplicates.data if form.handle_duplicates.data else "",
      options = form.handle_duplicates.choices,
      hint = form.handle_duplicates.description
    ) }}
    {% if form.handle_duplicates.errors %}
      <div class="text-xs text-destructive mt-1">
        <ul>
          {% for error in form.handle_duplicates.errors %}<li>{{ error }}</li>{% endfor %}
        </ul>
      </div>
    {% endif %}

    {{ form_group(
      label = form.default_password.label.text,
      name = form.default_password.name,
      type = "password",
      required = form.default_password.flags.required,
      hint = form.default_password.description,
      placeholder = "Enter default password for users"
    ) }}
    {% if form.default_password.errors %}
      <div class="text-xs text-destructive mt-1">
        <ul>
          {% for error in form.default_password.errors %}<li>{{ error }}</li>{% endfor %}
        </ul>
      </div>
    {% endif %}

    {{ form_group(
      label = form.send_welcome_email.label.text,
      name = form.send_welcome_email.name,
      type = "select",
      value = form.send_welcome_email.data if form.send_welcome_email.data else "",
      options = form.send_welcome_email.choices,
      hint = form.send_welcome_email.description
    ) }}
    {% if form.send_welcome_email.errors %}
      <div class="text-xs text-destructive mt-1">
        <ul>
          {% for error in form.send_welcome_email.errors %}<li>{{ error }}</li>{% endfor %}
        </ul>
      </div>
    {% endif %}

    <!-- File Format Help -->
    <div class="bg-muted/30 border border-border rounded-lg p-4">
      <h4 class="text-sm font-medium mb-2">File Format Requirements:</h4>
      <div class="text-xs text-muted-foreground space-y-2">
        <div>
          <strong>Required columns:</strong> <code>name</code>, <code>email</code>
        </div>
        <div>
          <strong>Optional User columns:</strong> <code>role</code>, <code>password</code>, <code>is_active</code>
        </div>
        <div>
          <strong>Optional Employee columns:</strong> <code>employee_number</code>, <code>first_name</code>, <code>last_name</code>, <code>job_title</code>, <code>phone</code>, <code>emp_type</code>, <code>business_unit_code</code>, <code>business_segment_code</code>, <code>hire_date</code>
        </div>
        <div>
          <strong>Date format:</strong> YYYY-MM-DD, MM/DD/YYYY, or DD/MM/YYYY
        </div>
        <div>
          <strong>Role values:</strong> User, Manager, Admin
        </div>
        <div>
          <strong>Employee types:</strong> Full-time, Part-time, Contract, Intern
        </div>
        <div>
          <strong>Status values:</strong> active, inactive (or true/false, 1/0, yes/no)
        </div>
      </div>
    </div>
  </div>

  <div class="flex justify-end space-x-2 pt-4">
    <button type="button" class="btn btn-outline btn-md drawer-close">Cancel</button>
    <button type="submit" class="btn btn-primary btn-md">
      <i data-lucide="upload" class="w-4 h-4 mr-2"></i>
      Import Users
    </button>
  </div>
</form>

<script>
document.addEventListener('DOMContentLoaded', function() {
  const form = document.getElementById('bulkImportDrawerForm');
  if (form) {
    form.addEventListener('submit', function(e) {
      e.preventDefault();
      
      const submitBtn = form.querySelector('button[type="submit"]');
      const originalText = submitBtn.innerHTML;
      
      // Show loading state
      submitBtn.disabled = true;
      submitBtn.innerHTML = '<i data-lucide="loader-2" class="w-4 h-4 mr-2 animate-spin"></i>Processing...';
      
      // Create FormData object
      const formData = new FormData(form);
      
      // Submit via fetch
      fetch(form.action, {
        method: 'POST',
        body: formData,
        headers: {
          'X-Requested-With': 'XMLHttpRequest'
        }
      })
      .then(response => response.json())
      .then(data => {
        if (data.success) {
          // Show success message
          if (typeof showToast === 'function') {
            showToast(data.message, { type: 'success' });
          }
          
          // Close drawer
          if (window.drawerManager) {
            window.drawerManager.closeLast();
          }
          
          // Reload page to show updated data
          setTimeout(() => {
            window.location.reload();
          }, 1000);
        } else {
          // Show error message
          if (typeof showToast === 'function') {
            showToast(data.message, { type: 'error' });
          }
          
          // Show detailed errors if available
          if (data.details && data.details.error_messages) {
            data.details.error_messages.forEach(error => {
              if (typeof showToast === 'function') {
                showToast(error, { type: 'error' });
              }
            });
          }
        }
      })
      .catch(error => {
        console.error('Import error:', error);
        if (typeof showToast === 'function') {
          showToast('Import failed. Please try again.', { type: 'error' });
        }
      })
      .finally(() => {
        // Reset button state
        submitBtn.disabled = false;
        submitBtn.innerHTML = originalText;
        
        // Re-initialize Lucide icons
        if (typeof lucide !== 'undefined' && lucide.createIcons) {
          lucide.createIcons();
        }
      });
    });
  }
});
</script>
