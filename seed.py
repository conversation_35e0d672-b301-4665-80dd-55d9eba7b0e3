#!/usr/bin/env python
"""
Enhanced seed script to initialize database with required data.
Run this after migrations to set up the database with initial data.

Features:
- Interactive table clearing confirmation for individual and bulk operations
- Dynamic seeder auto-detection from the seeders/ directory
- Maintains existing command-line interface compatibility
"""
import click
import os
import sys
import argparse
import importlib
import inspect
from typing import Dict, Callable, List, Tuple
from app import create_app, db
from app.utils.settings import initialize_default_settings
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()

# Create app with proper configuration
config_name = os.environ.get('FLASK_CONFIG', 'development')
app = create_app(config_name)

# Dynamic seeder discovery
SEEDERS_DIR = os.path.join(os.path.dirname(__file__), 'seeders')
discovered_seeders: Dict[str, Callable] = {}
seeder_metadata: Dict[str, Dict] = {}

def discover_seeders() -> None:
    """
    Dynamically discover and import seeder functions from the seeders/ directory.

    This function scans the seeders directory for Python files and automatically
    imports seeder functions that follow the naming convention 'seed_*'.
    """
    global discovered_seeders, seeder_metadata

    # Import the clear_database function from db_utils
    try:
        from seeders.db_utils import clear_database
        discovered_seeders['clear_database'] = clear_database
    except ImportError as e:
        click.echo(f"Warning: Could not import clear_database function: {e}")

    # Scan seeders directory for Python files
    if not os.path.exists(SEEDERS_DIR):
        click.echo(f"Warning: Seeders directory not found at {SEEDERS_DIR}")
        return

    for filename in os.listdir(SEEDERS_DIR):
        if filename.endswith('.py') and filename != '__init__.py' and filename != 'db_utils.py':
            module_name = filename[:-3]  # Remove .py extension
            module_path = f'seeders.{module_name}'

            try:
                # Import the module
                module = importlib.import_module(module_path)

                # Find all functions in the module that start with 'seed_'
                for name, obj in inspect.getmembers(module, inspect.isfunction):
                    if name.startswith('seed_') and obj.__module__ == module_path:
                        discovered_seeders[name] = obj

                        # Extract metadata from function docstring
                        doc = obj.__doc__ or ""
                        first_line = doc.split('\n')[0].strip() if doc else f"Seed {name[5:]} data"

                        # Map function name to command name (remove 'seed_' prefix)
                        command_name = name[5:]  # Remove 'seed_' prefix
                        if command_name.endswith('_types'):
                            command_name = command_name[:-6]  # Remove '_types' suffix for attendance_types
                        elif command_name == 'business_units':
                            command_name = 'business'  # Keep compatibility

                        seeder_metadata[command_name] = {
                            'function': obj,
                            'description': first_line,
                            'module': module_name,
                            'function_name': name
                        }

            except ImportError as e:
                click.echo(f"Warning: Could not import {module_path}: {e}")
            except Exception as e:
                click.echo(f"Error processing {module_path}: {e}")

def prompt_clear_confirmation(table_name: str, is_bulk: bool = False) -> bool:
    """
    Prompt user for confirmation before clearing table data.

    Args:
        table_name (str): Name of the table or 'all tables' for bulk operations
        is_bulk (bool): Whether this is a bulk operation

    Returns:
        bool: True if user confirms, False otherwise
    """
    if is_bulk:
        message = "Do you want to clear all tables before seeding? (y/N): "
    else:
        message = f"Do you want to clear the '{table_name}' table before seeding? (y/N): "

    response = click.prompt(message, default='N', show_default=False).strip().lower()
    return response in ['y', 'yes']

def clear_specific_table(table_name: str) -> bool:
    """
    Clear a specific table based on the seeder type.

    Args:
        table_name (str): The name of the table/seeder type to clear

    Returns:
        bool: True if successful, False otherwise
    """
    from app import db
    from sqlalchemy import text
    from app.models import (
        User, EmployeeDetail, BusinessUnit, BusinessSegment,
        Team, TeamGroup, Setting, AttendanceType, Holiday
    )

    try:
        # Map table names to appropriate clearing operations
        table_map = {
            'users': lambda: User.query.delete(),
            'business': lambda: _clear_business_tables(),
            'teams': lambda: _clear_team_tables(),
            'settings': lambda: Setting.query.filter(Setting.key != 'app_initialized').delete(),
            'attendance': lambda: AttendanceType.query.delete(),
            'holidays': lambda: Holiday.query.delete(),
        }

        if table_name in table_map:
            click.echo(f"Clearing {table_name} table(s)...")
            table_map[table_name]()
            db.session.commit()
            click.echo(f"✓ {table_name.title()} table(s) cleared successfully")
            return True
        else:
            click.echo(f"Warning: No clearing method defined for '{table_name}'")
            return False

    except Exception as e:
        try:
            db.session.rollback()
        except:
            pass  # db might not be available
        click.echo(f"Error clearing {table_name} table(s): {e}")
        return False

def _clear_business_tables():
    """Clear business-related tables in proper order."""
    from app import db
    from sqlalchemy import text

    # Clear in reverse dependency order
    db.session.execute(text('DELETE FROM employee_details WHERE business_unit_id IS NOT NULL OR business_segment_id IS NOT NULL'))
    from app.models import BusinessSegment, BusinessUnit
    BusinessSegment.query.delete()
    BusinessUnit.query.delete()

def _clear_team_tables():
    """Clear team-related tables in proper order."""
    from app import db
    from sqlalchemy import text
    from app.models import TeamGroup, Team

    # Clear association table first
    db.session.execute(text('DELETE FROM team_members'))
    TeamGroup.query.delete()
    Team.query.delete()

# Discover seeders on module import
discover_seeders()

def seed_business_combined():
    """
    Combined seeder for business units and segments.
    This maintains compatibility with the existing 'business' command.
    """
    results = {'created': 0, 'updated': 0, 'skipped': 0}

    # Seed business units first
    if 'seed_business_units' in discovered_seeders:
        unit_result = discovered_seeders['seed_business_units']()
        if isinstance(unit_result, dict):
            for key in results:
                results[key] += unit_result.get(key, 0)

    # Then seed business segments
    if 'seed_business_segments' in discovered_seeders:
        segment_result = discovered_seeders['seed_business_segments']()
        if isinstance(segment_result, dict):
            for key in results:
                results[key] += segment_result.get(key, 0)

    return results

# Add the combined business seeder to our metadata
if 'business' not in seeder_metadata:
    seeder_metadata['business'] = {
        'function': seed_business_combined,
        'description': 'Seed business units and segments',
        'module': 'combined',
        'function_name': 'seed_business_combined'
    }

@click.group()
def cli():
    """Database seeding commands with interactive table clearing and auto-discovery."""
    pass

def create_seeder_command(command_name: str, metadata: Dict) -> Callable:
    """
    Create a Click command for a discovered seeder function.

    Args:
        command_name (str): The command name (e.g., 'users', 'business')
        metadata (Dict): Metadata about the seeder function

    Returns:
        Callable: The Click command function
    """
    @click.option('--clear', '-c', is_flag=True, help='Clear the table before seeding')
    @click.option('--yes', '-y', is_flag=True, help='Skip confirmation prompts')
    def seeder_command(clear, yes):
        seeder_func = metadata['function']
        description = metadata['description']

        with app.app_context():
            try:
                # Handle clearing if requested
                if clear:
                    if yes or prompt_clear_confirmation(command_name):
                        if not clear_specific_table(command_name):
                            click.echo("Failed to clear table. Continuing with seeding...")
                    else:
                        click.echo("Table clearing cancelled. Continuing with seeding...")
                elif not yes:
                    # Prompt for table clearing if not forced clear and not skipping prompts
                    should_clear = prompt_clear_confirmation(command_name)
                    if should_clear:
                        if not clear_specific_table(command_name):
                            click.echo("Failed to clear table. Continuing with seeding...")

                click.echo(f"Seeding {command_name}...")
                result = seeder_func()

                # Handle different return types
                if isinstance(result, dict) and 'created' in result:
                    click.echo(f"✓ {command_name.title()} seeded successfully! "
                              f"Created: {result.get('created', 0)}, "
                              f"Updated: {result.get('updated', 0)}, "
                              f"Skipped: {result.get('skipped', 0)}")
                else:
                    click.echo(f"✓ {command_name.title()} seeded successfully!")

            except Exception as e:
                click.echo(f"✗ Error seeding {command_name}: {e}")
                raise e

    # Set function attributes for Click
    seeder_command.__name__ = f'cmd_seed_{command_name}'
    seeder_command.__doc__ = f"{metadata['description']}."

    return seeder_command

# Dynamically create Click commands for discovered seeders
for command_name, metadata in seeder_metadata.items():
    command_func = create_seeder_command(command_name, metadata)
    cli.command(command_name)(command_func)

@cli.command('all')
@click.option('--clear', '-c', is_flag=True, help='Clear all tables before seeding')
@click.option('--yes', '-y', is_flag=True, help='Skip confirmation prompts')
def cmd_seed_all(clear, yes):
    """Run all seeders in the correct order with optional table clearing."""
    with app.app_context():
        try:
            click.echo("Starting database seeding process...")

            # Handle clearing if requested
            if clear:
                if yes or prompt_clear_confirmation("all tables", is_bulk=True):
                    click.echo("\n=== Clearing All Tables ===")
                    from seeders.db_utils import clear_database
                    if not clear_database(confirm=False):
                        click.echo("Database clearing failed. Aborting seeding.")
                        return
                else:
                    click.echo("Table clearing cancelled. Continuing with seeding...")

            # Define seeding order for dependencies
            seeding_order = [
                'settings', 'users', 'business', 'teams', 'attendance', 'holidays'
            ]

            for seeder_name in seeding_order:
                if seeder_name in seeder_metadata:
                    click.echo(f"\n=== Seeding {seeder_name.title()} ===")
                    seeder_func = seeder_metadata[seeder_name]['function']

                    try:
                        result = seeder_func()

                        if isinstance(result, dict) and 'created' in result:
                            click.echo(f"✓ Created: {result.get('created', 0)}, "
                                      f"Updated: {result.get('updated', 0)}, "
                                      f"Skipped: {result.get('skipped', 0)}")
                        else:
                            click.echo(f"✓ {seeder_name.title()} completed successfully")

                    except Exception as e:
                        click.echo(f"✗ Error seeding {seeder_name}: {e}")
                        raise e
                else:
                    click.echo(f"Warning: Seeder '{seeder_name}' not found")

            click.echo("\n🎉 Database seeding completed successfully!")

        except Exception as e:
            click.echo(f"✗ Error during seeding: {e}")
            raise e

@cli.command('clear')
@click.option('--yes', '-y', is_flag=True, help='Skip confirmation prompt')
def cmd_clear_db(yes):
    """Clear all data from the database."""
    with app.app_context():
        try:
            click.echo("Clearing entire database...")
            from seeders.db_utils import clear_database
            if clear_database(confirm=not yes):
                click.echo("✓ Database cleared successfully!")
            else:
                click.echo("Database clearing aborted.")
        except Exception as e:
            click.echo(f"✗ Error clearing database: {e}")
            raise e

@cli.command('list')
def cmd_list_seeders():
    """List all available seeders with descriptions."""
    click.echo("Available seeders:")
    click.echo("=" * 50)

    for command_name, metadata in seeder_metadata.items():
        click.echo(f"  {command_name:<15} - {metadata['description']}")
        click.echo(f"  {'':>15}   Module: {metadata['module']}")
        click.echo(f"  {'':>15}   Function: {metadata['function_name']}")
        click.echo()

    click.echo(f"Total seeders discovered: {len(seeder_metadata)}")
    click.echo("\nUsage examples:")
    click.echo("  python seed.py users          # Seed users with interactive clearing prompt")
    click.echo("  python seed.py all --clear    # Seed all with table clearing")
    click.echo("  python seed.py list           # Show this list")

def run_all_seeders(clear_first: bool = False, skip_confirmation: bool = False) -> None:
    """
    Run all seeder functions to populate the database (legacy function for argparse).

    Args:
        clear_first (bool): Whether to clear the database before seeding
        skip_confirmation (bool): Whether to skip confirmation prompts
    """
    with app.app_context():
        try:
            print("Starting database seeding process...")

            # Clear database if requested
            if clear_first:
                print("\n=== Clearing Database ===")
                from seeders.db_utils import clear_database
                if not clear_database(confirm=not skip_confirmation):
                    print("Database seeding aborted.")
                    return

            # Define seeding order for dependencies
            seeding_order = [
                'settings', 'users', 'business', 'teams', 'attendance', 'holidays'
            ]

            for seeder_name in seeding_order:
                if seeder_name in seeder_metadata:
                    print(f"\n=== Seeding {seeder_name.title()} ===")
                    seeder_func = seeder_metadata[seeder_name]['function']

                    try:
                        result = seeder_func()

                        if isinstance(result, dict) and 'created' in result:
                            print(f"Created: {result.get('created', 0)}, "
                                 f"Updated: {result.get('updated', 0)}, "
                                 f"Skipped: {result.get('skipped', 0)}")
                        else:
                            print(f"{seeder_name.title()} completed successfully")

                    except Exception as e:
                        print(f"Error seeding {seeder_name}: {e}")
                        raise e
                else:
                    print(f"Warning: Seeder '{seeder_name}' not found")

            print("\nDatabase seeding completed successfully!")

        except Exception as e:
            print(f"Error during seeding: {e}")
            raise e

if __name__ == '__main__':
    # Check for legacy argparse usage (only --clear and/or --yes with no commands)
    if (len(sys.argv) > 1 and
        all(arg in ['--clear', '-c', '--yes', '-y'] or arg.startswith('seed.py') for arg in sys.argv) and
        not any(arg in seeder_metadata for arg in sys.argv) and
        'list' not in sys.argv and 'all' not in sys.argv):
        # Legacy argparse mode for backward compatibility
        parser = argparse.ArgumentParser(
            description='Enhanced seed script with interactive table clearing and auto-discovery.',
            epilog='Examples:\n'
                   '  python seed.py --clear --yes    # Clear all and seed all (no prompts)\n'
                   '  python seed.py users            # Seed users with interactive prompt\n'
                   '  python seed.py all --clear      # Seed all with interactive clearing\n'
                   '  python seed.py list             # List available seeders',
            formatter_class=argparse.RawDescriptionHelpFormatter
        )
        parser.add_argument('--clear', '-c', action='store_true',
                          help='Clear the database before seeding')
        parser.add_argument('--yes', '-y', action='store_true',
                          help='Skip confirmation prompts')
        args = parser.parse_args()
        run_all_seeders(clear_first=args.clear, skip_confirmation=args.yes)
    else:
        # Use Click interface (primary interface)
        cli()
