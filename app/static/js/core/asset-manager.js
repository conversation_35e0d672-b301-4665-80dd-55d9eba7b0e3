/**
 * Asset Manager
 *
 * This script optimizes asset loading for the application.
 */

// Define critical and non-critical assets
const CRITICAL_ASSETS = [
  '/static/js/core/critical-init.js',
  '/static/js/components/theme.js',
  '/static/js/components/page-loader.js', // Page loader for navigation feedback
  '/static/js/core/utils.js', // Utils.js moved to critical for handleFlashMessages
  '/static/js/components/sidebar.js' // Sidebar.js moved to critical for navigation functionality
];

const NON_CRITICAL_ASSETS = [
  // All critical components are now loaded directly in base.html
];

// Check if we need Chart.js for this page
const needsChartJs = document.getElementById('employeeDistributionChart') !== null ||
                    document.querySelector('[data-needs-chart]') !== null;

// Preload critical assets
CRITICAL_ASSETS.forEach(asset => {
  const link = document.createElement('link');
  link.rel = 'preload';
  link.href = asset;
  link.as = 'script';
  document.head.appendChild(link);
});

// Preload Chart.js if needed
if (needsChartJs) {
  const chartScript = document.createElement('script');
  chartScript.src = 'https://cdn.jsdelivr.net/npm/chart.js';
  chartScript.async = false; // Load synchronously to ensure it's available when needed
  document.head.appendChild(chartScript);
}

// Load non-critical assets with delay
function loadNonCriticalAssets() {
  // Wait until page is fully loaded and idle
  if (window.requestIdleCallback) {
    requestIdleCallback(() => {
      loadAssets();
    }, { timeout: 2000 });
  } else {
    // Fallback for browsers without requestIdleCallback
    setTimeout(loadAssets, 1000);
  }
}

function loadAssets() {
  NON_CRITICAL_ASSETS.forEach(asset => {
    const script = document.createElement('script');
    script.src = asset;
    script.async = true;
    document.body.appendChild(script);
  });
}

// Initialize when DOM is loaded
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', loadNonCriticalAssets);
} else {
  loadNonCriticalAssets();
}

// Optimize image loading
document.addEventListener('DOMContentLoaded', () => {
  // Lazy load images
  const lazyImages = document.querySelectorAll('img[loading="lazy"]');

  // Set up intersection observer for lazy loading
  if ('IntersectionObserver' in window) {
    const imageObserver = new IntersectionObserver((entries) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          const img = entry.target;
          const src = img.getAttribute('data-src');

          if (src) {
            img.src = src;
            img.removeAttribute('data-src');
          }

          imageObserver.unobserve(img);
        }
      });
    });

    lazyImages.forEach(img => {
      imageObserver.observe(img);
    });
  } else {
    // Fallback for browsers without IntersectionObserver
    lazyImages.forEach(img => {
      const src = img.getAttribute('data-src');
      if (src) {
        img.src = src;
        img.removeAttribute('data-src');
      }
    });
  }
});
