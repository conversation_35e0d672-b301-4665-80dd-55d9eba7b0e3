{% extends "base.html" %}

{% from "components/page_header.html" import page_header %}
{% from "components/button.html" import button, button_group %}
{% from "partials/forms/base_form.html" import form_group %}
{% from "components/analytics_card.html" import analytics_card, analytics_card_grid %}

{% block title %}Holiday Work Reports{% endblock %}

{% block header %}Holiday Work Reports{% endblock %}

{% block content %}
<div class="space-y-6">
  {{ page_header(
    title="Holiday Work Reports & Analytics",
    description="Comprehensive analytics and reporting for holiday work tracking and management."
  ) }}

  <!-- Date Range Filter -->
  <div class="mb-6 rounded-md border border-border bg-card shadow-sm overflow-hidden">
    <div class="border-b border-border px-5 py-4 flex justify-between items-center cursor-pointer hover:bg-accent/5 transition-colors duration-150" id="filter-toggle">
      <div class="flex items-center">
        <i data-lucide="calendar-range" class="h-4 w-4 mr-2 text-primary"></i>
        <h3 class="text-base font-medium">Report Period</h3>
        <span class="ml-2 inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-blue-50 text-blue-600 dark:bg-blue-900/30 dark:text-blue-400 border border-blue-200 dark:border-blue-800/30">
          <i data-lucide="calendar" class="h-3 w-3 mr-1"></i>
          {{ start_date.strftime('%b %d') }} - {{ end_date.strftime('%b %d, %Y') }}
        </span>
      </div>
      <span class="chevron-wrapper">
        <i data-lucide="chevron-down" class="h-4 w-4 text-muted-foreground" id="filter-chevron"></i>
      </span>
    </div>
    <div class="p-6 hidden" id="filter-content">
      <form method="GET" class="space-y-6">
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <!-- Start Date -->
          <div class="grid gap-1.5">
            <label for="start_date" class="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 flex items-center">
              <i data-lucide="calendar-range" class="h-3.5 w-3.5 mr-1.5 text-muted-foreground"></i>
              Start Date
            </label>
            <div class="relative">
              <input type="date" id="start_date" name="start_date" value="{{ start_date.strftime('%Y-%m-%d') }}"
                    class="flex h-9 w-full rounded-md border border-input bg-transparent px-3 py-1 pl-9 text-sm shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50 appearance-none"
                    placeholder="Select start date">
              <div class="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                <i data-lucide="calendar" class="h-4 w-4 text-muted-foreground"></i>
              </div>
            </div>
          </div>

          <!-- End Date -->
          <div class="grid gap-1.5">
            <label for="end_date" class="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 flex items-center">
              <i data-lucide="calendar-range" class="h-3.5 w-3.5 mr-1.5 text-muted-foreground"></i>
              End Date
            </label>
            <div class="relative">
              <input type="date" id="end_date" name="end_date" value="{{ end_date.strftime('%Y-%m-%d') }}"
                    class="flex h-9 w-full rounded-md border border-input bg-transparent px-3 py-1 pl-9 text-sm shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50 appearance-none"
                    placeholder="Select end date">
              <div class="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                <i data-lucide="calendar" class="h-4 w-4 text-muted-foreground"></i>
              </div>
            </div>
          </div>
        </div>

        <!-- Quick Date Presets -->
        <div class="pt-4 border-t border-border">
          <div class="flex items-center mb-3">
            <i data-lucide="clock" class="h-4 w-4 mr-2 text-muted-foreground"></i>
            <span class="text-sm font-medium text-foreground">Quick Presets</span>
          </div>
          <div class="flex flex-wrap gap-2">
            <button type="button" onclick="setDateRange('today')"
                    class="inline-flex items-center px-3 py-1.5 rounded-md text-sm bg-muted text-foreground hover:bg-muted/80 transition-colors">
              <i data-lucide="calendar" class="h-3.5 w-3.5 mr-1.5"></i>
              Today
            </button>
            <button type="button" onclick="setDateRange('week')"
                    class="inline-flex items-center px-3 py-1.5 rounded-md text-sm bg-muted text-foreground hover:bg-muted/80 transition-colors">
              <i data-lucide="calendar-days" class="h-3.5 w-3.5 mr-1.5"></i>
              This Week
            </button>
            <button type="button" onclick="setDateRange('month')"
                    class="inline-flex items-center px-3 py-1.5 rounded-md text-sm bg-muted text-foreground hover:bg-muted/80 transition-colors">
              <i data-lucide="calendar-range" class="h-3.5 w-3.5 mr-1.5"></i>
              This Month
            </button>
            <button type="button" onclick="setDateRange('quarter')"
                    class="inline-flex items-center px-3 py-1.5 rounded-md text-sm bg-muted text-foreground hover:bg-muted/80 transition-colors">
              <i data-lucide="calendar-check" class="h-3.5 w-3.5 mr-1.5"></i>
              This Quarter
            </button>
            <button type="button" onclick="setDateRange('year')"
                    class="inline-flex items-center px-3 py-1.5 rounded-md text-sm bg-muted text-foreground hover:bg-muted/80 transition-colors">
              <i data-lucide="calendar" class="h-3.5 w-3.5 mr-1.5"></i>
              This Year
            </button>
          </div>
        </div>

        <!-- Action Buttons -->
        <div class="flex justify-end space-x-2 pt-4">
          <a href="{{ url_for('admin.holiday_work_reports') }}"
             class="inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 border border-input bg-background hover:bg-accent hover:text-accent-foreground h-9 px-4 py-2">
            <i data-lucide="rotate-ccw" class="h-4 w-4 mr-2"></i>
            Reset
          </a>
          <button type="submit"
                  class="inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 bg-primary text-primary-foreground shadow hover:bg-primary/90 h-9 px-4 py-2">
            <i data-lucide="refresh-cw" class="h-4 w-4 mr-2"></i>
            Update Report
          </button>
        </div>
      </form>
    </div>
  </div>

  <!-- Overview Statistics -->
  {% call analytics_card_grid() %}
    {{ analytics_card(
        title="Total Holiday Work",
        value=overview_stats.total_holiday_work,
        description="Total records",
        icon="sun",
        color_scheme="amber"
    ) }}

    {{ analytics_card(
        title="Approved",
        value=overview_stats.approved_holiday_work,
        description="Approval rate",
        icon="check",
        color_scheme="green",
        badge_text=overview_stats.approval_rate ~ "%",
        badge_variant="success"
    ) }}

    {{ analytics_card(
        title="Pending",
        value=overview_stats.pending_holiday_work,
        description="Awaiting approval",
        icon="clock",
        color_scheme="yellow"
    ) }}

    {{ analytics_card(
        title="Employees",
        value=overview_stats.unique_employees,
        description="Unique workers",
        icon="users",
        color_scheme="blue"
    ) }}
  {% endcall %}

  <!-- Monthly Trends Chart -->
  <div class="card">
    <div class="card-header">
      <h3 class="card-title">Monthly Trends ({{ current_year }})</h3>
    </div>
    <div class="card-content">
      <div class="h-80">
        <canvas id="monthlyTrendsChart"></canvas>
      </div>
    </div>
  </div>

  <!-- Employee Rankings and Holiday Breakdown -->
  <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
    <!-- Employee Rankings -->
    <div class="card">
      <div class="card-header">
        <h3 class="card-title">Top Employees (Holiday Work)</h3>
      </div>
      <div class="card-content">
        {% if employee_rankings.rankings %}
          <div class="space-y-3">
            {% for employee in employee_rankings.rankings %}
            <div class="flex items-center justify-between p-3 bg-muted/30 rounded-lg">
              <div class="flex items-center">
                <div class="flex-shrink-0 w-8 h-8 bg-primary/10 rounded-full flex items-center justify-center">
                  <span class="text-sm font-medium text-primary">{{ employee.rank }}</span>
                </div>
                <div class="ml-3">
                  <p class="text-sm font-medium text-foreground">{{ employee.employee_name }}</p>
                  <p class="text-xs text-muted-foreground">{{ employee.employee_number }}</p>
                </div>
              </div>
              <div class="text-right">
                <p class="text-sm font-medium text-foreground">{{ employee.holiday_work_count }} records</p>
                <p class="text-xs text-muted-foreground">{{ employee.approval_rate }}% approved</p>
              </div>
            </div>
            {% endfor %}
          </div>
        {% else %}
          <div class="text-center py-8">
            <svg class="mx-auto h-12 w-12 text-muted-foreground" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
            </svg>
            <h3 class="mt-2 text-sm font-medium text-foreground">No holiday work records</h3>
            <p class="mt-1 text-sm text-muted-foreground">No employees have worked on holidays in this period.</p>
          </div>
        {% endif %}
      </div>
    </div>

    <!-- Holiday Breakdown -->
    <div class="card">
      <div class="card-header">
        <h3 class="card-title">Holiday Breakdown</h3>
      </div>
      <div class="card-content">
        {% if holiday_breakdown.holiday_breakdown %}
          <div class="space-y-3">
            {% for holiday in holiday_breakdown.holiday_breakdown[:10] %}
            <div class="flex items-center justify-between p-3 bg-muted/30 rounded-lg">
              <div>
                <p class="text-sm font-medium text-foreground">{{ holiday.holiday_name }}</p>
                <p class="text-xs text-muted-foreground">{{ holiday.date }} ({{ holiday.region_code }})</p>
              </div>
              <div class="text-right">
                <p class="text-sm font-medium text-foreground">{{ holiday.work_count }} employees</p>
                <p class="text-xs text-muted-foreground">{{ holiday.approval_rate }}% approved</p>
              </div>
            </div>
            {% endfor %}
          </div>
        {% else %}
          <div class="text-center py-8">
            <svg class="mx-auto h-12 w-12 text-muted-foreground" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3a4 4 0 118 0v4m-4 8a4 4 0 11-8 0v-1a4 4 0 014-4h4a4 4 0 014 4v1a4 4 0 11-8 0z"></path>
            </svg>
            <h3 class="mt-2 text-sm font-medium text-foreground">No holiday work</h3>
            <p class="mt-1 text-sm text-muted-foreground">No work was performed on holidays in this period.</p>
          </div>
        {% endif %}
      </div>
    </div>
  </div>

  <!-- Export Actions -->
  <div class="card">
    <div class="card-header">
      <h3 class="card-title">Export Reports</h3>
    </div>
    <div class="card-content">
      <div class="flex flex-wrap gap-2">
        {% set export_buttons = [
          {"text": "Export Overview", "variant": "outline", "onclick": "exportOverview()", "icon": "download"},
          {"text": "Export Employee Rankings", "variant": "outline", "onclick": "exportEmployeeRankings()", "icon": "download"},
          {"text": "Export Holiday Breakdown", "variant": "outline", "onclick": "exportHolidayBreakdown()", "icon": "download"}
        ] %}
        {{ button_group(export_buttons) }}
      </div>
    </div>
  </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
// Monthly Trends Chart
document.addEventListener('DOMContentLoaded', function() {
  const ctx = document.getElementById('monthlyTrendsChart').getContext('2d');

  const monthlyData = {{ monthly_trends.monthly_trends | tojson }};
  const labels = monthlyData.map(d => d.month_name);
  const totalData = monthlyData.map(d => d.total);
  const approvedData = monthlyData.map(d => d.approved);

  new Chart(ctx, {
    type: 'line',
    data: {
      labels: labels,
      datasets: [{
        label: 'Total Holiday Work',
        data: totalData,
        borderColor: 'rgb(245, 158, 11)',
        backgroundColor: 'rgba(245, 158, 11, 0.1)',
        tension: 0.1
      }, {
        label: 'Approved Holiday Work',
        data: approvedData,
        borderColor: 'rgb(34, 197, 94)',
        backgroundColor: 'rgba(34, 197, 94, 0.1)',
        tension: 0.1
      }]
    },
    options: {
      responsive: true,
      maintainAspectRatio: false,
      plugins: {
        legend: {
          position: 'top',
        },
        title: {
          display: true,
          text: 'Holiday Work Trends by Month'
        }
      },
      scales: {
        y: {
          beginAtZero: true,
          ticks: {
            stepSize: 1
          }
        }
      }
    }
  });
});

// Filter toggle functionality
document.addEventListener('DOMContentLoaded', function() {
  const filterToggle = document.getElementById('filter-toggle');
  const filterContent = document.getElementById('filter-content');

  if (filterToggle && filterContent) {
    // Function to update the expanded state of the chevron wrapper
    function updateExpandedState() {
      const isContentVisible = !filterContent.classList.contains('hidden');
      const chevronWrapper = filterToggle.querySelector('.chevron-wrapper');

      if (chevronWrapper) {
        if (isContentVisible) {
          chevronWrapper.classList.add('expanded');
        } else {
          chevronWrapper.classList.remove('expanded');
        }
      }
    }

    // Toggle content visibility and update expanded state when clicked
    filterToggle.addEventListener('click', function() {
      filterContent.classList.toggle('hidden');
      updateExpandedState();
    });

    // Initialize expanded state on page load
    setTimeout(() => {
      updateExpandedState();
    }, 100);
  }
});

// Date range preset functions
function setDateRange(preset) {
  const today = new Date();
  const startDateInput = document.getElementById('start_date');
  const endDateInput = document.getElementById('end_date');

  let startDate, endDate;

  switch(preset) {
    case 'today':
      startDate = new Date(today);
      endDate = new Date(today);
      break;

    case 'week':
      const dayOfWeek = today.getDay();
      const diff = today.getDate() - dayOfWeek + (dayOfWeek === 0 ? -6 : 1); // Adjust when day is Sunday
      startDate = new Date(today.setDate(diff));
      endDate = new Date(startDate);
      endDate.setDate(startDate.getDate() + 6);
      break;

    case 'month':
      startDate = new Date(today.getFullYear(), today.getMonth(), 1);
      endDate = new Date(today.getFullYear(), today.getMonth() + 1, 0);
      break;

    case 'quarter':
      const quarter = Math.floor(today.getMonth() / 3);
      startDate = new Date(today.getFullYear(), quarter * 3, 1);
      endDate = new Date(today.getFullYear(), quarter * 3 + 3, 0);
      break;

    case 'year':
      startDate = new Date(today.getFullYear(), 0, 1);
      endDate = new Date(today.getFullYear(), 11, 31);
      break;

    default:
      return;
  }

  // Format dates as YYYY-MM-DD for input
  const formatDate = (date) => {
    return date.toISOString().split('T')[0];
  };

  if (startDateInput) startDateInput.value = formatDate(startDate);
  if (endDateInput) endDateInput.value = formatDate(endDate);
}

// Export functions
function exportOverview() {
  const data = {{ overview_stats | tojson }};
  downloadJSON(data, 'holiday_work_overview.json');
}

function exportEmployeeRankings() {
  const data = {{ employee_rankings | tojson }};
  downloadJSON(data, 'holiday_work_employee_rankings.json');
}

function exportHolidayBreakdown() {
  const data = {{ holiday_breakdown | tojson }};
  downloadJSON(data, 'holiday_work_breakdown.json');
}

function downloadJSON(data, filename) {
  const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
  const url = URL.createObjectURL(blob);
  const a = document.createElement('a');
  a.href = url;
  a.download = filename;
  document.body.appendChild(a);
  a.click();
  document.body.removeChild(a);
  URL.revokeObjectURL(url);
}
</script>
{% endblock %}
