/**
 * Page Loader CSS
 *
 * Styles for the global page loading overlay
 */

/* Page loader overlay */
.page-loader {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(4px);
  z-index: 9999;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  transition: opacity 0.3s ease, visibility 0.3s ease;
}

/* Dark mode support */
.dark .page-loader {
  background: rgba(0, 0, 0, 0.95);
}

/* Hide loader */
.page-loader.hidden {
  opacity: 0;
  visibility: hidden;
  pointer-events: none;
}

/* Loader content */
.page-loader-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
  max-width: 300px;
  text-align: center;
}

/* Loader spinner */
.page-loader-spinner {
  width: 64px;
  height: 64px;
  opacity: 0.8;
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
}

/* Dark mode spinner */
.dark .page-loader-spinner {
  filter: drop-shadow(0 2px 4px rgba(255, 255, 255, 0.1));
}

/* Loader text */
.page-loader-text {
  font-size: 1rem;
  font-weight: 500;
  color: hsl(var(--foreground));
  opacity: 0.8;
  margin-top: 0.5rem;
}

/* Loading dots animation */
.page-loader-dots {
  display: inline-block;
  margin-left: 0.25rem;
}

.page-loader-dots::after {
  content: '';
  animation: page-loader-dots 1.5s ease-in-out infinite;
}

/* Progress bar (optional) */
.page-loader-progress {
  width: 200px;
  height: 2px;
  background: rgba(0, 0, 0, 0.1);
  border-radius: 1px;
  overflow: hidden;
  margin-top: 1rem;
}

.dark .page-loader-progress {
  background: rgba(255, 255, 255, 0.1);
}

.page-loader-progress-bar {
  height: 100%;
  background: hsl(var(--primary));
  border-radius: 1px;
  width: 0%;
  transition: width 0.3s ease;
  animation: page-loader-progress 2s ease-in-out infinite;
}

/* Animations */
@keyframes page-loader-dots {
  0%, 20% {
    content: '';
  }
  40% {
    content: '.';
  }
  60% {
    content: '..';
  }
  80%, 100% {
    content: '...';
  }
}

@keyframes page-loader-progress {
  0% {
    width: 0%;
  }
  50% {
    width: 70%;
  }
  100% {
    width: 100%;
  }
}

/* Remove fade-in animation - loader shows instantly */
.page-loader.show {
  opacity: 1;
}

/* Responsive adjustments */
@media (max-width: 640px) {
  .page-loader-spinner {
    width: 48px;
    height: 48px;
  }

  .page-loader-text {
    font-size: 0.875rem;
  }

  .page-loader-progress {
    width: 150px;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .page-loader-spinner {
    animation: none;
  }

  .page-loader-dots::after {
    animation: none;
    content: '...';
  }

  .page-loader-progress-bar {
    animation: none;
  }
}
