{% extends "base.html" %}
{% from 'admin/components/key_metrics.html' import key_metrics %}
{% from 'admin/components/activity_trends.html' import activity_trends %}
{% from 'admin/components/department_activity.html' import department_activity %}
{% from 'admin/components/user_behavior_dashboard.html' import user_behavior_dashboard %}
{% from 'admin/components/activity_distribution.html' import activity_distribution %}
{% from 'admin/components/hidden_data.html' import hidden_data %}
{% from 'components/analytics_card.html' import analytics_card, analytics_card_small, analytics_card_grid %}

{% block title %}Analytics{% endblock %}

{% block header %}Analytics{% endblock %}

{% block styles %}
<!-- Include analytics CSS -->
<link rel="stylesheet" href="{{ url_for('static', filename='css/analytics.css') }}">
{% endblock %}

{% block content %}
<!-- Analytics Header -->
<div class="mb-8">
    <div class="analytics-card bg-white dark:bg-gray-800 rounded-xl shadow-lg border border-gray-200 dark:border-gray-700 overflow-hidden">
        <div class="p-6 md:p-8 bg-gradient-to-r from-blue-50/50 via-blue-50/20 to-transparent dark:from-blue-900/20 dark:via-blue-900/10 dark:to-transparent">
            <div class="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
                <div class="flex items-start gap-4">
                    <div class="hidden sm:flex items-center justify-center w-12 h-12 rounded-lg bg-blue-100 dark:bg-blue-900/30 text-blue-600 dark:text-blue-400 shadow-sm">
                        <i data-lucide="bar-chart-2" class="w-6 h-6"></i>
                    </div>
                    <div>
                        <h1 class="text-2xl md:text-3xl font-bold text-gray-900 dark:text-white analytics-text">Analytics Dashboard</h1>
                        <p class="text-gray-600 dark:text-gray-400 mt-1.5 analytics-text">Monitor user activity and engagement metrics</p>
                    </div>
                </div>
                <div class="flex flex-wrap items-center gap-3">
                    <div class="text-sm text-gray-600 dark:text-gray-400 bg-gray-50 dark:bg-gray-700/50 px-3 py-1.5 rounded-full flex items-center">
                        <i data-lucide="calendar" class="w-3.5 h-3.5 mr-1.5"></i>
                        <span id="period-display">Last 30 days</span>
                    </div>
                    <button class="inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 text-gray-700 dark:text-gray-200 hover:bg-gray-50 dark:hover:bg-gray-700 h-9 px-4 py-2 shadow-sm" aria-label="Refresh Analytics" id="refresh-analytics-btn">
                        <i data-lucide="refresh-cw" class="refresh-icon w-4 h-4 mr-2"></i>
                        <span>Refresh</span>
                    </button>
                    <button class="inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 bg-blue-600 text-white hover:bg-blue-700 dark:bg-blue-600 dark:hover:bg-blue-500 h-9 px-4 py-2 shadow-sm" aria-label="Export Data">
                        <i data-lucide="download" class="w-4 h-4 mr-2"></i>
                        <span>Export</span>
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Stats Overview -->
<div class="mb-10">
    <div class="flex flex-wrap items-center justify-between mb-6">
        <h2 class="text-xl font-semibold text-gray-900 dark:text-white flex items-center analytics-text">
            <i data-lucide="bar-chart-2" class="w-5 h-5 text-blue-600 dark:text-blue-400 mr-2"></i>
            <span>Key Metrics</span>
        </h2>
        <div class="flex items-center mt-2 sm:mt-0">
            <div class="flex items-center space-x-2">
                <div class="relative inline-block">
                    <select class="appearance-none pl-3 pr-8 py-1.5 text-sm rounded-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-700 dark:text-gray-200 focus:outline-none focus:ring-2 focus:ring-blue-500 cursor-pointer">
                        <option value="7">Last 7 days</option>
                        <option value="30" selected>Last 30 days</option>
                        <option value="90">Last 90 days</option>
                    </select>
                    <div class="pointer-events-none absolute inset-y-0 right-0 flex items-center px-2 text-gray-700 dark:text-gray-300">
                        <i data-lucide="chevron-down" class="w-4 h-4"></i>
                    </div>
                </div>
                <div class="keyMetrics-loading hidden">
                    <div class="w-5 h-5 border-2 border-blue-200 border-t-blue-600 rounded-full animate-spin"></div>
                </div>
            </div>
        </div>
    </div>

    <!-- Key Metrics Component -->
    {{ key_metrics(total_users, active_users, visitors, page_views, avg_time, bounce_rate) }}
</div>

<!-- Main Analytics Content -->
<div class="grid grid-cols-1 lg:grid-cols-3 gap-6 md:gap-8 mb-8" style="min-height: 400px;">
    <!-- Traffic Overview Chart -->
    <div class="lg:col-span-2 flex flex-col">
        {{ activity_trends() }}
    </div>

    <!-- Department Activity -->
    <div class="lg:col-span-1 flex flex-col">
        {{ department_activity(department_data) }}
    </div>
</div>

<!-- User Behavior Dashboard -->
<div class="mt-6 mb-8">
    {{ user_behavior_dashboard(active_users, total_users, avg_sessions, avg_pages_per_session, retention_rate, user_behavior_data) }}
</div>

<!-- Activity Distribution -->
<div class="mb-6">
    {{ activity_distribution(activity_labels, activity_data) }}
</div>

<!-- Hidden data elements for JavaScript -->
{{ hidden_data(activity_dates, activity_counts, user_counts, activity_labels, activity_data, entity_labels, entity_data) }}
{% endblock %}

{% block scripts %}
<!-- Include Chart.js -->
<script src="https://cdn.jsdelivr.net/npm/chart.js@4.3.0/dist/chart.umd.min.js"></script>

<!-- Activity distribution chart is initialized in its own component -->
<script>
// No initialization needed here as the component handles it
</script>

<!-- Include analytics JavaScript -->
<script src="{{ url_for('static', filename='js/analytics.js') }}"></script>

<!-- Include system health JavaScript -->
<script src="{{ url_for('static', filename='js/system-health.js') }}"></script>
{% endblock %}
