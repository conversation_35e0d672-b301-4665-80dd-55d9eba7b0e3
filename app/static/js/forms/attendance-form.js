/**
 * Attendance Form Module - Simplified Version
 *
 * This module handles conditional validation for attendance record forms.
 * The holiday work reason field is always visible but only required when holiday work is checked.
 */

/**
 * Global initialization function for attendance forms
 * Called by the drawer system when forms are loaded dynamically
 */
window.initializeAttendanceForm = function() {
  // Holiday work conditional validation
  function setupHolidayWorkValidation() {
    const holidayCheckbox = document.getElementById('is_holiday_work');
    const reasonTextarea = document.getElementById('holiday_work_reason');

    if (!holidayCheckbox || !reasonTextarea) {
      return; // Holiday work section not visible (likely create mode)
    }

    // Remove existing event listeners to prevent duplicates
    const newCheckbox = holidayCheckbox.cloneNode(true);
    holidayCheckbox.parentNode.replaceChild(newCheckbox, holidayCheckbox);

    // Add fresh event listener for validation
    newCheckbox.addEventListener('change', function() {
      if (this.checked) {
        reasonTextarea.setAttribute('required', 'required');
      } else {
        reasonTextarea.removeAttribute('required');
        reasonTextarea.value = '';
      }
    });

    // Set initial state
    if (newCheckbox.checked) {
      reasonTextarea.setAttribute('required', 'required');
    } else {
      reasonTextarea.removeAttribute('required');
    }
  }

  // Attendance type functionality for drawer
  function setupAttendanceType() {
    const attendanceTypeSelect = document.getElementById('attendance_type_id');
    const timeFields = document.getElementById('timeFields');
    const durationField = document.getElementById('durationField');

    if (!attendanceTypeSelect) {
      return;
    }

    function toggleFields() {
      if (!timeFields || !durationField) return;

      const selectedOption = attendanceTypeSelect.options[attendanceTypeSelect.selectedIndex];
      const startTimeField = document.getElementById('start_time');
      const endTimeField = document.getElementById('end_time');
      const durationHoursField = document.getElementById('duration_hours');

      if (selectedOption && selectedOption.dataset.isFullDay === 'false') {
        timeFields.style.display = 'block';
        durationField.style.display = 'block';

        // Make time and duration fields required for partial day types
        if (startTimeField) startTimeField.setAttribute('required', 'required');
        if (endTimeField) endTimeField.setAttribute('required', 'required');
        if (durationHoursField) durationHoursField.setAttribute('required', 'required');
      } else {
        timeFields.style.display = 'none';
        durationField.style.display = 'none';

        // Remove required attributes for full day types
        if (startTimeField) {
          startTimeField.removeAttribute('required');
          startTimeField.value = '';
        }
        if (endTimeField) {
          endTimeField.removeAttribute('required');
          endTimeField.value = '';
        }
        if (durationHoursField) {
          durationHoursField.removeAttribute('required');
          durationHoursField.value = '';
        }
      }
    }

    function updateStatusBasedOnType() {
      const statusSelect = document.getElementById('status');
      const selectedOption = attendanceTypeSelect ? attendanceTypeSelect.options[attendanceTypeSelect.selectedIndex] : null;

      if (selectedOption && statusSelect && selectedOption.value) {
        const requiresApproval = selectedOption.dataset.requiresApproval === 'true';

        if (!requiresApproval) {
          statusSelect.value = 'Auto-Approved';
        } else {
          statusSelect.value = 'Pending';
        }
      }
    }

    // Remove existing event listeners to prevent duplicates
    const newSelect = attendanceTypeSelect.cloneNode(true);
    attendanceTypeSelect.parentNode.replaceChild(newSelect, attendanceTypeSelect);

    // Add fresh event listener
    newSelect.addEventListener('change', function() {
      toggleFields();
      updateStatusBasedOnType();
    });

    // Set initial states
    toggleFields();
    updateStatusBasedOnType();
  }

  // Initialize all form elements with a small delay to ensure DOM is ready
  setTimeout(function() {
    setupHolidayWorkValidation();
    setupAttendanceType();
  }, 100);
};

/**
 * Initialize attendance form for regular page loads (non-drawer)
 */
function initializeAttendanceFormOnPageLoad() {
  // Only run if we're not in a drawer context
  if (document.querySelector('.drawer-content')) {
    return;
  }

  // Core form variables
  const attendanceTypeSelect = document.getElementById('attendance_type_id');
  const timeFields = document.getElementById('timeFields');
  const durationField = document.getElementById('durationField');

  function toggleFields() {
    if (!attendanceTypeSelect || !timeFields || !durationField) return;

    const selectedOption = attendanceTypeSelect.options[attendanceTypeSelect.selectedIndex];
    const startTimeField = document.getElementById('start_time');
    const endTimeField = document.getElementById('end_time');
    const durationHoursField = document.getElementById('duration_hours');

    if (selectedOption && selectedOption.dataset.isFullDay === 'false') {
      timeFields.style.display = 'block';
      durationField.style.display = 'block';

      // Make time and duration fields required for partial day types
      if (startTimeField) startTimeField.setAttribute('required', 'required');
      if (endTimeField) endTimeField.setAttribute('required', 'required');
      if (durationHoursField) durationHoursField.setAttribute('required', 'required');
    } else {
      timeFields.style.display = 'none';
      durationField.style.display = 'none';

      // Remove required attributes for full day types
      if (startTimeField) {
        startTimeField.removeAttribute('required');
        startTimeField.value = '';
      }
      if (endTimeField) {
        endTimeField.removeAttribute('required');
        endTimeField.value = '';
      }
      if (durationHoursField) {
        durationHoursField.removeAttribute('required');
        durationHoursField.value = '';
      }
    }
  }

  function updateStatusBasedOnType() {
    const statusSelect = document.getElementById('status');
    const selectedOption = attendanceTypeSelect ? attendanceTypeSelect.options[attendanceTypeSelect.selectedIndex] : null;

    if (selectedOption && statusSelect && selectedOption.value) {
      const requiresApproval = selectedOption.dataset.requiresApproval === 'true';

      if (!requiresApproval) {
        statusSelect.value = 'Auto-Approved';
      } else {
        statusSelect.value = 'Pending';
      }
    }
  }

  // Holiday work conditional validation
  function setupHolidayWorkValidation() {
    const holidayCheckbox = document.getElementById('is_holiday_work');
    const reasonTextarea = document.getElementById('holiday_work_reason');

    if (!holidayCheckbox || !reasonTextarea) return; // Holiday work section not visible (likely create mode)

    function toggleRequired() {
      if (holidayCheckbox.checked) {
        reasonTextarea.setAttribute('required', 'required');
      } else {
        reasonTextarea.removeAttribute('required');
        reasonTextarea.value = '';
      }
    }

    holidayCheckbox.addEventListener('change', toggleRequired);

    // Set initial state
    toggleRequired();
  }

  function initializeFormElements() {
    // Initialize attendance type fields
    if (attendanceTypeSelect) {
      attendanceTypeSelect.addEventListener('change', function() {
        toggleFields();
        updateStatusBasedOnType();
      });
    }

    // Initialize holiday work validation
    setupHolidayWorkValidation();

    // Set initial states
    toggleFields();
    updateStatusBasedOnType();
  }

  // Initialize immediately if DOM is ready, otherwise wait
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initializeFormElements);
  } else {
    // Delay to ensure all elements are ready
    setTimeout(initializeFormElements, 50);
  }
}

// Initialize for regular page loads
initializeAttendanceFormOnPageLoad();
