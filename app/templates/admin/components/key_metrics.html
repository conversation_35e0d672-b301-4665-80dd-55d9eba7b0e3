{% from 'components/analytics_card.html' import analytics_card_small, analytics_card_grid %}

{% macro key_metrics(total_users, active_users, visitors, page_views, avg_time, bounce_rate) %}
{% call analytics_card_grid() %}
    {{ analytics_card_small(
        title="Total Users",
        value=total_users,
        description="Registered accounts",
        icon="users",
        color_scheme="blue"
    ) }}

    {{ analytics_card_small(
        title="Active Users",
        value=active_users,
        description="Enabled accounts",
        icon="user-check",
        color_scheme="green"
    ) }}

    {{ analytics_card_small(
        title="Page Views",
        value=page_views,
        description="Total activity count",
        icon="activity",
        color_scheme="indigo",
        badge_text="+8%",
        badge_variant="info"
    ) }}

    {{ analytics_card_small(
        title="System Health",
        value="Excellent",
        description="Based on system logs",
        icon="activity-square",
        color_scheme="emerald"
    ) }}
{% endcall %}
{% endmacro %}
