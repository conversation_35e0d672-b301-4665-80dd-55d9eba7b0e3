{% extends "base.html" %}

{% from "components/page_header.html" import page_header %}
{% from "components/table_header.html" import table_header %}
{% from "components/simple_table.html" import simple_table %}
{% from "components/action_buttons.html" import action_buttons %}

{% block title %}Users{% endblock %}

{% block header %}Users{% endblock %}

{% block content %}
<div class="space-y-6">
  <div class="flex justify-between items-center mb-6">
    <div>
      <h1 class="text-2xl font-bold">User Management</h1>
      <p class="text-sm text-muted-foreground mt-1">Manage your users and their permissions</p>
    </div>
    <div class="flex space-x-2">
      <button onclick="drawerManager.openForm('user_bulk_import')" class="btn btn-outline page-header-button px-4 py-2 rounded flex items-center">
        <i data-lucide="upload" class="w-4 h-4 mr-2"></i>
        <span>Import Users</span>
      </button>
      <button onclick="drawerManager.openForm('user')" class="btn btn-primary page-header-button px-4 py-2 rounded flex items-center">
        <i data-lucide="plus" class="w-4 h-4 mr-2"></i>
        <span>Add User</span>
      </button>
    </div>
  </div>

  <div class="card">
    {{ table_header(
      title="Users List"
    ) }}

    {% call simple_table(
      headers=[
        {'label': 'ID'},
        {'label': 'Name'},
        {'label': 'Email'},
        {'label': 'Role'},
        {'label': 'Status'},
        {'label': 'Actions', 'align': 'right'}
      ],
      items=users,
      empty_icon="users",
      empty_title="No users found",
      empty_description="Add users to get started",
      empty_button_text="Add User",
      empty_button_icon="plus",
      empty_button_action="drawerManager.openForm('user')"
    ) %}
      {% for user in users %}
      <tr class="border-b border-border hover:bg-muted/30">
        <td class="px-4 py-3 text-sm">{{ user.id }}</td>
        <td class="px-4 py-3 text-sm">{{ user.name }}</td>
        <td class="px-4 py-3 text-sm">{{ user.email }}</td>
        <td class="px-4 py-3 text-sm">
          {% if user.role == 'Admin' %}
            <span class="badge badge-primary">Admin</span>
          {% elif user.role == 'Manager' %}
            <span class="badge badge-success">Manager</span>
          {% else %}
            <span class="badge badge-secondary">{{ user.role }}</span>
          {% endif %}
        </td>
        <td class="px-4 py-3 text-sm">
          {% if user.is_active %}
            <span class="badge badge-success">Active</span>
          {% else %}
            <span class="badge badge-destructive">Inactive</span>
          {% endif %}
        </td>
        <td class="px-4 py-3 text-sm text-right">
          {{ action_buttons([
            {'type': 'button', 'action': 'drawerManager.openForm(\'user\', ' ~ user.id ~ ')', 'icon': 'edit', 'title': 'Edit'},
            {'type': 'button', 'action': 'confirmDeleteUser(' ~ user.id ~ ', \'' ~ user.name ~ '\')', 'icon': 'trash-2', 'variant': 'text-destructive', 'title': 'Delete'}
          ]) }}
        </td>
      </tr>
      {% endfor %}
    {% endcall %}

    {% include "partials/pagination.html" %}
  </div>
</div>
{% endblock %}

{% block scripts %}
<script src="{{ url_for('static', filename='js/pages/users.js') }}"></script>
<script>
  // Lucide icons are now initialized automatically
  document.addEventListener('DOMContentLoaded', function() {

      // We're now using flash messages converted to toasts via handleFlashMessages() in utils.js
      // This happens automatically on page load and provides a consistent notification experience

      // Clean URL parameters if they exist (for backward compatibility)
      const urlParams = new URLSearchParams(window.location.search);
      if (urlParams.has('status')) {
          const newUrl = window.location.pathname;
          window.history.replaceState({}, document.title, newUrl);
      }
  });

  // Confirm delete user
  function confirmDeleteUser(userId, userName) {
      showAlertDialog({
          title: 'Delete User',
          description: `Are you sure you want to delete ${userName}? This action cannot be undone.`,
          variant: 'destructive',
          onConfirm: () => {
              // Submit form to delete endpoint
              const form = document.createElement('form');
              form.method = 'POST';
              form.action = `{{ url_for('admin.delete_user', user_id=0) }}`.replace('0', userId);

              const csrfToken = document.createElement('input');
              csrfToken.type = 'hidden';
              csrfToken.name = 'csrf_token';
              csrfToken.value = '{{ csrf_token() }}';

              form.appendChild(csrfToken);
              document.body.appendChild(form);
              form.submit();
          }
      });
  }
</script>
{% endblock %}
