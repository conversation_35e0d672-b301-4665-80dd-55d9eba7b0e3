# Page Loader System

A comprehensive page loading system for the Matrix application that provides visual feedback during page navigation, form submissions, and asynchronous operations.

## Features

- **Automatic Navigation Loading**: Shows loader during page navigation via links, forms, and browser navigation
- **Manual Control**: Programmatic control over loader visibility and text
- **Form Integration**: Specialized handling for form submissions with automatic state management
- **AJAX Support**: Helper functions for async operations
- **Configurable Timing**: Default 3-second automatic display duration for all navigation
- **Theme Support**: Automatically adapts to light/dark themes
- **Accessibility**: Supports reduced motion preferences
- **Performance Optimized**: Minimal impact on page performance

## Files

- **CSS**: `/static/css/page-loader.css` - Styles and animations
- **JavaScript**: `/static/js/components/page-loader.js` - Core loader class
- **Utils**: `/static/js/utils/page-loader-utils.js` - Helper functions
- **Demo**: `/templates/demo/page-loader.html` - Interactive demo page

## Basic Usage

### Automatic Loading

The loader automatically shows for **all navigation** with a **3-second display duration**:
- <PERSON> clicks (unless they have `data-no-loader` attribute)
- Form submissions (unless they have `data-no-loader` attribute)
- Browser back/forward navigation
- Programmatic navigation (history.pushState/replaceState)

**Key Features:**
- **Zero Configuration**: Works automatically without modifying existing links
- **3-Second Duration**: Shows for exactly 3 seconds to provide consistent feedback
- **Smart Detection**: Intercepts all navigation types including browser buttons
- **Smooth Experience**: Prevents navigation flashing by ensuring minimum display time

```html
<!-- These will show the 3-second loader automatically -->
<a href="/dashboard">Go to Dashboard</a>
<a href="/users">View Users</a>
<form action="/submit" method="post">
    <button type="submit">Submit</button>
</form>

<!-- These will NOT show the loader -->
<a href="/page" data-no-loader>No Loader</a>
<a href="#section">Hash Link</a>
<a href="mailto:<EMAIL>">Email Link</a>
<a href="/file.pdf" target="_blank">New Tab</a>
<form action="/submit" data-no-loader>
    <button type="submit">Submit</button>
</form>
```

### Manual Control

```javascript
// Basic usage
showPageLoader();                           // Show with default text
showPageLoader('Loading data...');          // Show with custom text
hidePageLoader();                          // Hide the loader

// Check status
const isActive = isPageLoaderActive();     // Returns boolean

// Force hide (emergency)
forceHidePageLoader();                     // Immediately hide

// Update text while showing
updatePageLoaderText('New message...');    // Change displayed text
```

### Form Integration

```javascript
// Manual form handling
const form = document.getElementById('myForm');
showLoaderForForm(form, 'Submitting...');

// Simulate form processing
setTimeout(() => {
    hideLoaderForForm(form);
}, 2000);

// The form integration automatically:
// - Disables form elements
// - Shows loading state
// - Restores form state when hidden
```

### AJAX Operations

```javascript
// Method 1: Manual control
showPageLoader('Loading data...');
fetch('/api/data')
    .then(response => response.json())
    .then(data => {
        // Process data
    })
    .finally(() => {
        hidePageLoader();
    });

// Method 2: Using helper function
const data = await showLoaderForAjax(
    fetch('/api/data'),
    'Loading data...'
);

// Method 3: Using withPageLoader wrapper
const result = await withPageLoader(async () => {
    const response = await fetch('/api/data');
    return response.json();
}, 'Loading data...');
```

## Advanced Usage

### Progressive Text Updates

```javascript
showPageLoader('Step 1: Initializing...');

setTimeout(() => {
    updatePageLoaderText('Step 2: Loading data...');
}, 1000);

setTimeout(() => {
    updatePageLoaderText('Step 3: Processing...');
}, 2000);

setTimeout(() => {
    hidePageLoader();
}, 3000);
```

### Error Handling

```javascript
try {
    showPageLoader('Processing...');
    await someAsyncOperation();
    hidePageLoader();
} catch (error) {
    hidePageLoader();
    // Handle error
    console.error('Operation failed:', error);
}
```

### Custom Integration

```javascript
// Integration with existing loading patterns
class DataManager {
    async loadData() {
        return withPageLoader(async () => {
            const response = await fetch('/api/data');
            if (!response.ok) {
                throw new Error('Failed to load data');
            }
            return response.json();
        }, 'Loading data...');
    }
}
```

## Customization

### CSS Variables

The loader uses CSS custom properties that can be customized:

```css
:root {
    --page-loader-bg: rgba(255, 255, 255, 0.95);
    --page-loader-text: hsl(var(--foreground));
    --page-loader-spinner-size: 64px;
}

.dark {
    --page-loader-bg: rgba(0, 0, 0, 0.95);
}
```

### Loader Configuration

```javascript
// Access the loader instance for advanced configuration
const loader = window.pageLoader;

// Customize automatic display duration (default is 3000ms = 3 seconds)
loader.minimumDuration = 2000; // 2 seconds for faster navigation
loader.minimumDuration = 5000; // 5 seconds for slower connections

// Check if loader is active
const isLoading = loader.isLoading();
```

## Best Practices

### When to Use the Loader

✅ **Use for:**
- Page navigation
- Form submissions that reload the page
- Long-running operations (>500ms)
- Operations that block user interaction

❌ **Don't use for:**
- Very quick operations (<200ms)
- Background updates that don't block UI
- Real-time data updates
- Operations with their own loading indicators

### Performance Tips

1. **Preload Assets**: The loader image is preloaded for instant display
2. **Minimum Duration**: Loader shows for a minimum duration to avoid flashing
3. **Automatic Cleanup**: Loader automatically hides on page unload/visibility change
4. **Debounced Updates**: Text updates are optimized to prevent excessive DOM manipulation

### Accessibility

- Respects `prefers-reduced-motion` setting
- Provides proper ARIA labels
- Uses semantic HTML structure
- Maintains focus management

## Browser Support

- Modern browsers (Chrome 60+, Firefox 55+, Safari 12+, Edge 79+)
- Graceful degradation for older browsers
- Mobile browser support

## Troubleshooting

### Loader Not Showing

1. Check if page loader CSS is loaded
2. Verify JavaScript is not blocked
3. Ensure `window.pageLoader` exists
4. Check for `data-no-loader` attributes

### Loader Not Hiding

1. Use `forceHidePageLoader()` as emergency reset
2. Check for JavaScript errors in console
3. Verify proper error handling in async operations

### Performance Issues

1. Avoid showing loader for very quick operations
2. Don't call show/hide rapidly in succession
3. Use `withPageLoader` for better automatic management

## Examples in Codebase

The page loader is integrated in several places:

- **Form submissions**: `js/components/form-utils.js`
- **Analytics refresh**: `js/analytics.js`
- **Navigation**: Automatic via event listeners
- **Base template**: Included in all pages via `templates/base.html`

## API Reference

### Global Functions

| Function | Parameters | Description |
|----------|------------|-------------|
| `showPageLoader(text?)` | `text: string` | Show loader with optional text |
| `hidePageLoader()` | None | Hide the loader |
| `forceHidePageLoader()` | None | Emergency hide |
| `updatePageLoaderText(text)` | `text: string` | Update displayed text |
| `isPageLoaderActive()` | None | Check if loader is showing |
| `withPageLoader(operation, text?)` | `operation: Promise\|Function, text: string` | Wrap operation with loader |
| `showLoaderForForm(form, text?)` | `form: HTMLElement, text: string` | Form-specific loader |
| `hideLoaderForForm(form)` | `form: HTMLElement` | Hide form loader |
| `showLoaderForAjax(request, text?)` | `request: Promise, text: string` | AJAX-specific loader |

### PageLoader Class Methods

| Method | Parameters | Description |
|--------|------------|-------------|
| `show(text?)` | `text: string` | Show loader |
| `hide()` | None | Hide loader |
| `forceHide()` | None | Emergency hide |
| `updateText(text)` | `text: string` | Update text |
| `isLoading()` | None | Check status |
