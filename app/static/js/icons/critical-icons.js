/**
 * List of critical icons included in the sprite
 * This file is auto-generated by build-icon-sprite.js
 */
export const criticalIcons = [
  "panel-left",
  "chevron-down",
  "chevron-right",
  "chevron-up",
  "chevron-left",
  "menu",
  "home",
  "file-text",
  "users",
  "settings",
  "log-out",
  "bell",
  "alert-triangle",
  "alert-circle",
  "check-circle",
  "x-circle",
  "info",
  "x",
  "check",
  "plus",
  "minus",
  "edit",
  "trash",
  "copy",
  "sun",
  "moon",
  "monitor",
  "search",
  "scan-search",
  "user",
  "calendar",
  "calendar-days",
  "calendar-range",
  "calendar-check",
  "clock",
  "bar-chart",
  "trash-2",
  "refresh-cw",
  "bar-chart-2",
  "line-chart",
  "users-round",
  "briefcase",
  "layers",
  "activity",
  "eye",
  "database",
  "shield",
  "history",
  "key",
  "arrow-left",
  "building",
  "mail",
  "save",
  "file",
  "filter",
  "tag",
  "external-link",
  "layout-dashboard",
  "lock",
  "phone",
  "search-x",
  "user-plus",
  "arrow-right",
  "list",
  "shield-alert",
  "user-check",
  "user-cog",
  "hash",
  "toggle-left",
  "clipboard-list",
  "square-activity",
  "activity-square",
  "sliders-horizontal",
  "sliders",
  "git-branch",
  "funnel-x",
  "filter-x",
  "palette",
  "bug",
  "settings-2",
  "git-merge",
  "message-square",
  "rotate-ccw",
  "download",
  "upload",
  "alert-octagon",
  "user-minus",
  "chevrons-up-down",
  "globe",
  "badge",
  "bar-chart-3",
  "users-x",
  "id-card",
  "file-bar-chart",
  "repeat",
  "heart-pulse",
  "grid",
  "zap",
  "chart-pie",
  "trending-up"
];
