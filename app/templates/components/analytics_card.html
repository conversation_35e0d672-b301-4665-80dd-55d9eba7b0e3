{#
Analytics Card Component Macro
A reusable component for creating consistent analytics statistics cards across the application.
Used in analytics dashboard, holiday work reports, and other analytics views.
#}

{% macro analytics_card(
    title,
    value,
    description=None,
    icon=None,
    color_scheme="blue",
    badge_text=None,
    badge_variant="info",
    show_trend=False,
    trend_direction=None,
    trend_value=None,
    additional_classes="",
    click_action=None
) %}
{%- set color_schemes = {
    "blue": {
        "gradient": "from-blue-50/30 via-blue-50/20 to-transparent dark:from-blue-900/10 dark:via-blue-900/5 dark:to-transparent",
        "icon_bg": "bg-blue-100 dark:bg-blue-900/20",
        "icon_text": "text-blue-600 dark:text-blue-400",
        "badge_bg": "bg-blue-100 dark:bg-blue-900/30",
        "badge_text": "text-blue-800 dark:text-blue-400",
        "badge_border": "border-blue-200 dark:border-blue-800/30"
    },
    "green": {
        "gradient": "from-green-50/30 via-green-50/20 to-transparent dark:from-green-900/10 dark:via-green-900/5 dark:to-transparent",
        "icon_bg": "bg-green-100 dark:bg-green-900/20",
        "icon_text": "text-green-600 dark:text-green-400",
        "badge_bg": "bg-green-100 dark:bg-green-900/30",
        "badge_text": "text-green-800 dark:text-green-400",
        "badge_border": "border-green-200 dark:border-green-800/30"
    },
    "amber": {
        "gradient": "from-amber-50/30 via-amber-50/20 to-transparent dark:from-amber-900/10 dark:via-amber-900/5 dark:to-transparent",
        "icon_bg": "bg-amber-100 dark:bg-amber-900/20",
        "icon_text": "text-amber-600 dark:text-amber-400",
        "badge_bg": "bg-amber-100 dark:bg-amber-900/30",
        "badge_text": "text-amber-800 dark:text-amber-400",
        "badge_border": "border-amber-200 dark:border-amber-800/30"
    },
    "indigo": {
        "gradient": "from-indigo-50/30 via-indigo-50/20 to-transparent dark:from-indigo-900/10 dark:via-indigo-900/5 dark:to-transparent",
        "icon_bg": "bg-indigo-100 dark:bg-indigo-900/20",
        "icon_text": "text-indigo-600 dark:text-indigo-400",
        "badge_bg": "bg-indigo-100 dark:bg-indigo-900/30",
        "badge_text": "text-indigo-800 dark:text-indigo-400",
        "badge_border": "border-indigo-200 dark:border-indigo-800/30"
    },
    "purple": {
        "gradient": "from-purple-50/30 via-purple-50/20 to-transparent dark:from-purple-900/10 dark:via-purple-900/5 dark:to-transparent",
        "icon_bg": "bg-purple-100 dark:bg-purple-900/20",
        "icon_text": "text-purple-600 dark:text-purple-400",
        "badge_bg": "bg-purple-100 dark:bg-purple-900/30",
        "badge_text": "text-purple-800 dark:text-purple-400",
        "badge_border": "border-purple-200 dark:border-purple-800/30"
    },
    "yellow": {
        "gradient": "from-yellow-50/30 via-yellow-50/20 to-transparent dark:from-yellow-900/10 dark:via-yellow-900/5 dark:to-transparent",
        "icon_bg": "bg-yellow-100 dark:bg-yellow-900/20",
        "icon_text": "text-yellow-600 dark:text-yellow-400",
        "badge_bg": "bg-yellow-100 dark:bg-yellow-900/30",
        "badge_text": "text-yellow-800 dark:text-yellow-400",
        "badge_border": "border-yellow-200 dark:border-yellow-800/30"
    },
    "emerald": {
        "gradient": "from-emerald-50/30 via-emerald-50/20 to-transparent dark:from-blue-900/10 dark:via-blue-900/5 dark:to-transparent",
        "icon_bg": "bg-emerald-100 dark:bg-blue-900/20",
        "icon_text": "text-emerald-600 dark:text-blue-400",
        "badge_bg": "bg-emerald-100 dark:bg-blue-900/30",
        "badge_text": "text-emerald-800 dark:text-blue-400",
        "badge_border": "border-emerald-200 dark:border-blue-800/30"
    },
    "orange": {
        "gradient": "from-orange-50/30 via-orange-50/20 to-transparent dark:from-orange-900/10 dark:via-orange-900/5 dark:to-transparent",
        "icon_bg": "bg-orange-100 dark:bg-orange-900/20",
        "icon_text": "text-orange-600 dark:text-orange-400",
        "badge_bg": "bg-orange-100 dark:bg-orange-900/30",
        "badge_text": "text-orange-800 dark:text-orange-400",
        "badge_border": "border-orange-200 dark:border-orange-800/30"
    }
} -%}

{%- set colors = color_schemes.get(color_scheme, color_schemes["blue"]) -%}

{%- set badge_variants = {
    "info": {
        "bg": colors.badge_bg,
        "text": colors.badge_text,
        "border": colors.badge_border
    },
    "success": {
        "bg": "bg-green-100 dark:bg-green-900/30",
        "text": "text-green-800 dark:text-green-400",
        "border": "border-green-200 dark:border-green-800/30"
    },
    "warning": {
        "bg": "bg-amber-100 dark:bg-amber-900/30",
        "text": "text-amber-800 dark:text-amber-400",
        "border": "border-amber-200 dark:border-amber-800/30"
    },
    "error": {
        "bg": "bg-red-100 dark:bg-red-900/30",
        "text": "text-red-800 dark:text-red-400",
        "border": "border-red-200 dark:border-red-800/30"
    }
} -%}

{%- set badge_styles = badge_variants.get(badge_variant, badge_variants["info"]) -%}

<div class="analytics-card analytics-stat-card bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 shadow-md hover:shadow-lg transition-all duration-200 overflow-hidden {{ additional_classes }}"
     {% if click_action %}
     role="button"
     tabindex="0"
     onclick="{{ click_action }}"
     onkeydown="if(event.key==='Enter'||event.key===' '){{ click_action }}"
     style="cursor: pointer;"
     {% endif %}>
  <div class="p-5 md:p-6 bg-gradient-to-br {{ colors.gradient }}">
    <div class="flex justify-between items-start">
      <div class="flex-1 min-w-0">
        <p class="text-sm font-medium text-gray-600 dark:text-gray-400 analytics-text">{{ title }}</p>
        <div class="flex items-baseline gap-2 mt-2">
          <p class="text-3xl font-bold text-gray-900 dark:text-white analytics-text truncate">{{ value }}</p>
          {% if badge_text %}
          <span class="text-xs px-1.5 py-0.5 rounded-full {{ badge_styles.bg }} {{ badge_styles.text }} border {{ badge_styles.border }} flex-shrink-0">{{ badge_text }}</span>
          {% endif %}
          {% if show_trend and trend_direction and trend_value %}
          <span class="flex items-center text-xs {% if trend_direction == 'up' %}text-green-600 dark:text-green-400{% elif trend_direction == 'down' %}text-red-600 dark:text-red-400{% else %}text-gray-600 dark:text-gray-400{% endif %} flex-shrink-0">
            {% if trend_direction == 'up' %}
            <i data-lucide="trending-up" class="w-3 h-3 mr-1"></i>
            {% elif trend_direction == 'down' %}
            <i data-lucide="trending-down" class="w-3 h-3 mr-1"></i>
            {% else %}
            <i data-lucide="minus" class="w-3 h-3 mr-1"></i>
            {% endif %}
            {{ trend_value }}
          </span>
          {% endif %}
        </div>
        {% if description %}
        <p class="text-xs text-gray-500 dark:text-gray-400 mt-2 analytics-text">{{ description }}</p>
        {% endif %}
      </div>
      {% if icon %}
      <div class="rounded-full p-3 {{ colors.icon_bg }} {{ colors.icon_text }} shadow-sm flex-shrink-0 ml-4">
        <i data-lucide="{{ icon }}" class="w-6 h-6"></i>
      </div>
      {% endif %}
    </div>
  </div>
</div>
{% endmacro %}

{#
Small Analytics Card Variant
A more compact version for use in smaller spaces or inline cards.
#}

{% macro analytics_card_small(
    title,
    value,
    description=None,
    icon=None,
    color_scheme="blue",
    badge_text=None,
    badge_variant="info",
    additional_classes="",
    click_action=None
) %}
{%- set color_schemes = {
    "blue": {
        "bg": "bg-gray-50 dark:bg-gray-800/60",
        "icon_bg": "bg-blue-100 dark:bg-blue-900/30",
        "icon_text": "text-blue-600 dark:text-blue-400"
    },
    "green": {
        "bg": "bg-gray-50 dark:bg-gray-800/60",
        "icon_bg": "bg-green-100 dark:bg-green-900/30",
        "icon_text": "text-green-600 dark:text-green-400"
    },
    "purple": {
        "bg": "bg-gray-50 dark:bg-gray-800/60",
        "icon_bg": "bg-purple-100 dark:bg-purple-900/30",
        "icon_text": "text-purple-600 dark:text-purple-400"
    },
    "indigo": {
        "bg": "bg-gray-50 dark:bg-gray-800/60",
        "icon_bg": "bg-indigo-100 dark:bg-indigo-900/30",
        "icon_text": "text-indigo-600 dark:text-indigo-400"
    },
    "orange": {
        "bg": "bg-gray-50 dark:bg-gray-800/60",
        "icon_bg": "bg-orange-100 dark:bg-orange-900/30",
        "icon_text": "text-orange-600 dark:text-orange-400"
    }
} -%}

{%- set colors = color_schemes.get(color_scheme, color_schemes["blue"]) -%}

{%- set badge_variants = {
    "info": {
        "bg": "bg-blue-100 dark:bg-blue-900/30",
        "text": "text-blue-800 dark:text-blue-400",
        "border": "border-blue-200 dark:border-blue-800/30"
    },
    "success": {
        "bg": "bg-green-100 dark:bg-green-900/30",
        "text": "text-green-800 dark:text-green-400",
        "border": "border-green-200 dark:border-green-800/30"
    },
    "warning": {
        "bg": "bg-amber-100 dark:bg-amber-900/30",
        "text": "text-amber-800 dark:text-amber-400",
        "border": "border-amber-200 dark:border-amber-800/30"
    },
    "error": {
        "bg": "bg-red-100 dark:bg-red-900/30",
        "text": "text-red-800 dark:text-red-400",
        "border": "border-red-200 dark:border-red-800/30"
    }
} -%}

{%- set badge_styles = badge_variants.get(badge_variant, badge_variants["info"]) -%}

<div class="{{ colors.bg }} rounded-lg p-4 border border-gray-200 dark:border-gray-700 shadow-sm hover:shadow-md transition-all duration-200 {{ additional_classes }}"
     {% if click_action %}
     role="button"
     tabindex="0"
     onclick="{{ click_action }}"
     onkeydown="if(event.key==='Enter'||event.key===' '){{ click_action }}"
     style="cursor: pointer;"
     {% endif %}>
  <div class="flex items-center justify-between mb-1">
    <p class="text-sm font-medium text-gray-700 dark:text-gray-300 analytics-text">{{ title }}</p>
    {% if icon %}
    <div class="w-8 h-8 rounded-full {{ colors.icon_bg }} flex items-center justify-center">
      <i data-lucide="{{ icon }}" class="w-4 h-4 {{ colors.icon_text }}"></i>
    </div>
    {% endif %}
  </div>
  <div class="flex items-baseline gap-2">
    <p class="text-2xl font-bold text-gray-900 dark:text-white analytics-text">{{ value }}</p>
    {% if badge_text %}
    <span class="text-xs px-1.5 py-0.5 rounded-full {{ badge_styles.bg }} {{ badge_styles.text }} border {{ badge_styles.border }} flex-shrink-0">{{ badge_text }}</span>
    {% endif %}
  </div>
  {% if description %}
  <p class="text-xs text-gray-500 dark:text-gray-400 mt-1 analytics-text">{{ description }}</p>
  {% endif %}
</div>
{% endmacro %}

{#
Analytics Card Grid Container
A utility macro for consistent grid layouts of analytics cards.
#}

{% macro analytics_card_grid(grid_cols="1 sm:grid-cols-2 lg:grid-cols-4", gap="5 md:gap-6", additional_classes="") %}
<div class="grid grid-cols-{{ grid_cols }} gap-{{ gap }} analytics-grid-sm {{ additional_classes }}">
  {{ caller() }}
</div>
{% endmacro %}
