"""
Admin holiday management routes.
"""

from flask import render_template, redirect, url_for, flash, request, jsonify, current_app
from flask_login import login_required
from datetime import datetime, date
from sqlalchemy import or_

from app import db
from app.models.attendance import Holiday
from app.models import Activity
from app.services.holiday_service import HolidayService
from app.utils.decorators import admin_required, log_activity
from app.utils.pagination import paginate_query
from app.utils.ajax_helpers import ajax_response
from app.routes.admin import admin_bp
from app.forms.admin_holiday import HolidayForm, BulkImportForm


@admin_bp.route('/holidays')
@login_required
@admin_required
@log_activity('Viewed holidays list', entity_type='Holiday', category=Activity.CATEGORY_ADMIN, method=Activity.METHOD_READ)
def holidays():
    """Display paginated list of holidays with filtering."""
    # Get filter parameters
    region_filter = request.args.get('region', '')
    search_query = request.args.get('search', '')
    start_date = request.args.get('start_date', '')
    end_date = request.args.get('end_date', '')

    # Build the query
    query = Holiday.query

    # Apply filters
    if region_filter:
        query = query.filter(Holiday.region_code == region_filter)

    if search_query:
        query = query.filter(
            or_(
                Holiday.name.ilike(f'%{search_query}%'),
                Holiday.description.ilike(f'%{search_query}%')
            )
        )

    if start_date:
        try:
            start_date_obj = datetime.strptime(start_date, '%Y-%m-%d').date()
            query = query.filter(Holiday.date >= start_date_obj)
        except ValueError:
            flash('Invalid start date format', 'error')

    if end_date:
        try:
            end_date_obj = datetime.strptime(end_date, '%Y-%m-%d').date()
            query = query.filter(Holiday.date <= end_date_obj)
        except ValueError:
            flash('Invalid end date format', 'error')

    # Order by date
    query = query.order_by(Holiday.date.desc())

    # Use centralized pagination
    per_page = current_app.config.get('PAGINATION_PER_PAGE_ADMIN', 20)

    # Get total count before pagination
    total_count = query.count()  # Count all holidays matching filters

    holidays, pagination = paginate_query(query, per_page=per_page)

    # Get available regions for filter dropdown
    regions = db.session.query(Holiday.region_code).distinct().all()
    available_regions = [region[0] for region in regions if region[0]]

    return render_template('admin/holidays/index.html',
                          holidays=holidays,
                          total_count=total_count,
                          pagination=pagination,
                          available_regions=available_regions,
                          current_filters={
                              'region': region_filter,
                              'search': search_query,
                              'start_date': start_date,
                              'end_date': end_date
                          },
                          active_page='holidays')


@admin_bp.route('/holidays/<int:holiday_id>')
@login_required
@admin_required
def get_holiday(holiday_id):
    """Get holiday details in JSON format."""
    holiday = Holiday.query.get_or_404(holiday_id)
    return jsonify({
        'success': True,
        'holiday': holiday.to_dict()
    })


@admin_bp.route('/holidays/form', methods=['GET'])
@login_required
@admin_required
def get_holiday_form_create():
    """Returns the holiday creation form for loading into a drawer."""
    form = HolidayForm()
    return render_template('admin/holidays/partials/holiday_form.html',
                         form=form,
                         form_title="Add New Holiday",
                         action_url=url_for('admin.create_holiday'))


@admin_bp.route('/holidays/form/<int:holiday_id>', methods=['GET'])
@login_required
@admin_required
def get_holiday_form_edit(holiday_id):
    """Returns the holiday edit form for loading into a drawer."""
    holiday = Holiday.query.get_or_404(holiday_id)
    form = HolidayForm(obj=holiday, original_date=holiday.date, original_region=holiday.region_code)
    return render_template('admin/holidays/partials/holiday_form.html',
                         form=form,
                         form_title="Edit Holiday",
                         action_url=url_for('admin.update_holiday', holiday_id=holiday.id))


@admin_bp.route('/holidays/create', methods=['POST'])
@login_required
@admin_required
@ajax_response(success_redirect='admin.holidays')
def create_holiday():
    """Handles creating a new holiday."""
    form = HolidayForm()

    if form.validate_on_submit():
        new_holiday = Holiday()
        new_holiday.name = form.name.data
        new_holiday.date = form.date.data
        new_holiday.description = form.description.data
        new_holiday.region_code = form.region_code.data
        new_holiday.holiday_type = form.holiday_type.data

        db.session.add(new_holiday)
        try:
            db.session.commit()
            return {
                'success': True,
                'message': 'Holiday created successfully!',
                'redirect_url': url_for('admin.holidays')
            }
        except Exception as e:
            db.session.rollback()
            return {
                'success': False,
                'message': f'Error creating holiday: {str(e)}',
                'errors': form.errors
            }

    # Form validation failed
    return {
        'success': False,
        'message': 'Please correct the errors below.',
        'errors': form.errors
    }


@admin_bp.route('/holidays/<int:holiday_id>/edit', methods=['POST'])
@login_required
@admin_required
@ajax_response(success_redirect='admin.holidays')
def update_holiday(holiday_id):
    """Handles updating an existing holiday."""
    holiday = Holiday.query.get_or_404(holiday_id)
    form = HolidayForm(obj=holiday, original_date=holiday.date, original_region=holiday.region_code)

    if form.validate_on_submit():
        holiday.name = form.name.data
        holiday.date = form.date.data
        holiday.description = form.description.data
        holiday.region_code = form.region_code.data
        holiday.holiday_type = form.holiday_type.data

        try:
            db.session.commit()
            return {
                'success': True,
                'message': 'Holiday updated successfully!'
            }
        except Exception as e:
            db.session.rollback()
            return {
                'success': False,
                'message': f'Error updating holiday: {str(e)}',
                'errors': form.errors
            }

    # Form validation failed
    return {
        'success': False,
        'message': 'Please correct the errors below.',
        'errors': form.errors
    }


@admin_bp.route('/holidays/<int:holiday_id>/delete', methods=['POST'])
@login_required
@admin_required
@ajax_response(success_redirect='admin.holidays')
def delete_holiday(holiday_id):
    """Handles deleting a holiday."""
    holiday = Holiday.query.get_or_404(holiday_id)

    try:
        db.session.delete(holiday)
        db.session.commit()
        return {
            'success': True,
            'message': f'Holiday "{holiday.name}" deleted successfully!'
        }
    except Exception as e:
        db.session.rollback()
        return {
            'success': False,
            'message': f'Error deleting holiday "{holiday.name}": {str(e)}. It might be in use.'
        }


@admin_bp.route('/holidays/calendar')
@login_required
@admin_required
@log_activity('Viewed holiday calendar', entity_type='Holiday', category=Activity.CATEGORY_ADMIN, method=Activity.METHOD_READ)
def holiday_calendar():
    """Display holiday calendar view."""
    # Get parameters
    year = request.args.get('year', date.today().year, type=int)
    month = request.args.get('month', type=int)
    region_code = request.args.get('region', 'PH')

    # Get calendar data
    calendar_data = HolidayService.get_holiday_calendar_data(region_code, year, month)

    # Get available regions
    regions = db.session.query(Holiday.region_code).distinct().all()
    available_regions = [region[0] for region in regions if region[0]]

    return render_template('admin/holidays/calendar.html',
                          calendar_data=calendar_data,
                          available_regions=available_regions,
                          current_year=year,
                          current_month=month,
                          current_region=region_code,
                          active_page='holiday_calendar')


@admin_bp.route('/holidays/upcoming/<region_code>')
@login_required
@admin_required
def get_upcoming_holidays(region_code):
    """Get upcoming holidays for a region (API endpoint)."""
    days = request.args.get('days', 30, type=int)

    try:
        holidays = HolidayService.get_upcoming_holidays(region_code, days)
        return jsonify({
            'success': True,
            'region_code': region_code,
            'days': days,
            'holidays': [holiday.to_dict() for holiday in holidays]
        })
    except Exception as e:
        current_app.logger.error(f'Error getting upcoming holidays: {str(e)}')
        return jsonify({
            'success': False,
            'error': 'Failed to get upcoming holidays'
        }), 500


@admin_bp.route('/holidays/bulk-import', methods=['POST'])
@login_required
@admin_required
@log_activity('Bulk imported holidays', entity_type='Holiday', category=Activity.CATEGORY_DATA, method=Activity.METHOD_CREATE)
def bulk_import_holidays():
    """Bulk import holidays from CSV file."""
    import csv
    import io
    from werkzeug.utils import secure_filename

    # Check if file was uploaded
    if 'file' not in request.files:
        flash('No file selected', 'error')
        return redirect(url_for('admin.holidays'))

    file = request.files['file']
    if file.filename == '':
        flash('No file selected', 'error')
        return redirect(url_for('admin.holidays'))

    # Validate file extension
    if not file.filename.lower().endswith('.csv'):
        flash('Please upload a CSV file', 'error')
        return redirect(url_for('admin.holidays'))

    # Get form parameters
    default_region = request.form.get('region_code', 'GLOBAL')
    handle_existing = request.form.get('overwrite_existing', 'skip')

    try:
        # Read CSV content
        stream = io.StringIO(file.stream.read().decode("UTF8"), newline=None)
        csv_reader = csv.DictReader(stream)

        # Validate CSV headers
        required_headers = ['name', 'date']
        optional_headers = ['description', 'region_code', 'holiday_type']

        if not all(header in csv_reader.fieldnames for header in required_headers):
            flash(f'CSV must contain columns: {", ".join(required_headers)}. Optional: {", ".join(optional_headers)}', 'error')
            return redirect(url_for('admin.holidays'))

        # Process CSV rows
        created_count = 0
        updated_count = 0
        skipped_count = 0
        error_count = 0
        errors = []

        for row_num, row in enumerate(csv_reader, start=2):  # Start at 2 because row 1 is headers
            try:
                # Extract and validate data
                name = row.get('name', '').strip()
                date_str = row.get('date', '').strip()
                description = row.get('description', '').strip()
                region_code = row.get('region_code', default_region).strip().upper()
                holiday_type = row.get('holiday_type', 'regular').strip().lower()

                if not name or not date_str:
                    errors.append(f'Row {row_num}: Name and date are required')
                    error_count += 1
                    continue

                # Parse date
                try:
                    holiday_date = datetime.strptime(date_str, '%Y-%m-%d').date()
                except ValueError:
                    try:
                        holiday_date = datetime.strptime(date_str, '%m/%d/%Y').date()
                    except ValueError:
                        errors.append(f'Row {row_num}: Invalid date format. Use YYYY-MM-DD or MM/DD/YYYY')
                        error_count += 1
                        continue

                # Validate region
                if region_code not in ['US', 'PH', 'GLOBAL']:
                    errors.append(f'Row {row_num}: Invalid region code. Use US, PH, or GLOBAL')
                    error_count += 1
                    continue

                # Validate holiday type
                valid_types = ['regular', 'special-non-working', 'special-working', 'federal']
                if holiday_type not in valid_types:
                    errors.append(f'Row {row_num}: Invalid holiday type. Use: {", ".join(valid_types)}')
                    error_count += 1
                    continue

                # Check if holiday already exists
                existing_holiday = Holiday.query.filter(
                    Holiday.date == holiday_date,
                    Holiday.region_code == region_code
                ).first()

                if existing_holiday:
                    if handle_existing == 'skip':
                        skipped_count += 1
                        continue
                    elif handle_existing == 'error':
                        errors.append(f'Row {row_num}: Holiday already exists for {date_str} in {region_code}')
                        error_count += 1
                        continue
                    elif handle_existing == 'update':
                        # Update existing holiday
                        existing_holiday.name = name
                        existing_holiday.description = description if description else None
                        existing_holiday.holiday_type = holiday_type
                        updated_count += 1
                        continue

                # Create new holiday
                holiday = Holiday(
                    name=name,
                    date=holiday_date,
                    description=description if description else None,
                    region_code=region_code,
                    holiday_type=holiday_type
                )
                db.session.add(holiday)
                created_count += 1

            except Exception as e:
                errors.append(f'Row {row_num}: {str(e)}')
                error_count += 1
                continue

        # Commit changes
        if created_count > 0 or updated_count > 0:
            db.session.commit()

        # Show results
        if error_count == 0:
            flash(f'Import completed successfully! Created: {created_count}, Updated: {updated_count}, Skipped: {skipped_count}', 'success')
        else:
            flash(f'Import completed with errors. Created: {created_count}, Updated: {updated_count}, Skipped: {skipped_count}, Errors: {error_count}', 'warning')

            # Show first few errors
            if errors:
                for error in errors[:5]:  # Show first 5 errors
                    flash(error, 'error')
                if len(errors) > 5:
                    flash(f'... and {len(errors) - 5} more errors', 'error')

    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f'Error during bulk import: {str(e)}')
        flash('Error processing CSV file. Please check the format and try again.', 'error')

    return redirect(url_for('admin.holidays'))


@admin_bp.route('/holidays/export')
@login_required
@admin_required
@log_activity('Exported holidays', entity_type='Holiday', category=Activity.CATEGORY_ADMIN, method=Activity.METHOD_READ)
def export_holidays():
    """Export holidays to CSV file."""
    import csv
    import io
    from flask import make_response

    # Get filter parameters (same as list view)
    region_filter = request.args.get('region', '')
    search_query = request.args.get('search', '')
    start_date = request.args.get('start_date', '')
    end_date = request.args.get('end_date', '')

    # Build the query (same logic as list view)
    query = Holiday.query

    if region_filter:
        query = query.filter(Holiday.region_code == region_filter)

    if search_query:
        query = query.filter(
            or_(
                Holiday.name.ilike(f'%{search_query}%'),
                Holiday.description.ilike(f'%{search_query}%')
            )
        )

    if start_date:
        try:
            start_date_obj = datetime.strptime(start_date, '%Y-%m-%d').date()
            query = query.filter(Holiday.date >= start_date_obj)
        except ValueError:
            pass

    if end_date:
        try:
            end_date_obj = datetime.strptime(end_date, '%Y-%m-%d').date()
            query = query.filter(Holiday.date <= end_date_obj)
        except ValueError:
            pass

    # Get holidays
    holidays = query.order_by(Holiday.date.asc()).all()

    # Create CSV content
    output = io.StringIO()
    writer = csv.writer(output)

    # Write headers
    writer.writerow(['name', 'date', 'description', 'region_code', 'holiday_type'])

    # Write data
    for holiday in holidays:
        writer.writerow([
            holiday.name,
            holiday.date.strftime('%Y-%m-%d'),
            holiday.description or '',
            holiday.region_code,
            holiday.holiday_type
        ])

    # Create response
    response = make_response(output.getvalue())
    response.headers['Content-Type'] = 'text/csv'
    response.headers['Content-Disposition'] = 'attachment; filename=holidays.csv'

    return response


# Drawer Form Routes
@admin_bp.route('/holidays/form')
@login_required
@admin_required
def holiday_form():
    """Display holiday form in drawer for create operations."""
    form = HolidayForm()
    return render_template('admin/holidays/partials/holiday_form.html',
                          form=form,
                          action_url=url_for('admin.create_holiday_drawer'),
                          form_title="Add New Holiday")

@admin_bp.route('/holidays/form/<int:holiday_id>')
@login_required
@admin_required
def holiday_form_edit(holiday_id):
    """Display holiday form in drawer for edit operations."""
    holiday = Holiday.query.get_or_404(holiday_id)
    form = HolidayForm(obj=holiday)
    return render_template('admin/holidays/partials/holiday_form.html',
                          form=form,
                          action_url=url_for('admin.update_holiday_drawer', holiday_id=holiday_id),
                          form_title="Edit Holiday")


@admin_bp.route('/holidays/drawer/create', methods=['POST'])
@login_required
@admin_required
@ajax_response(success_redirect='admin.holidays')
@log_activity('Created holiday', entity_type='Holiday', category=Activity.CATEGORY_DATA, method=Activity.METHOD_CREATE)
def create_holiday_drawer():
    """Create a new holiday via drawer form."""
    form = HolidayForm()

    if form.validate_on_submit():
        # Create holiday
        holiday = Holiday()
        holiday.name = form.name.data
        holiday.date = form.date.data
        holiday.description = form.description.data
        holiday.region_code = form.region_code.data
        holiday.holiday_type = form.holiday_type.data

        try:
            db.session.add(holiday)
            db.session.commit()
            return {
                'success': True,
                'message': f'Holiday "{holiday.name}" created successfully!'
            }
        except Exception as e:
            db.session.rollback()
            current_app.logger.error(f'Error creating holiday: {str(e)}')
            return {
                'success': False,
                'message': 'Error creating holiday. Please try again.'
            }

    # Form validation failed
    error_messages = {field: errors for field, errors in form.errors.items()}
    return {
        'success': False,
        'message': 'Please correct the errors below.',
        'errors': error_messages
    }


@admin_bp.route('/holidays/drawer/<int:holiday_id>/edit', methods=['POST'])
@login_required
@admin_required
@ajax_response(success_redirect='admin.holidays')
@log_activity('Updated holiday', entity_type='Holiday', category=Activity.CATEGORY_DATA, method=Activity.METHOD_UPDATE)
def update_holiday_drawer(holiday_id):
    """Update an existing holiday via drawer form."""
    holiday = Holiday.query.get_or_404(holiday_id)
    form = HolidayForm(obj=holiday)
    form.original_holiday_id = holiday_id

    if form.validate_on_submit():
        # Update holiday
        holiday.name = form.name.data
        holiday.date = form.date.data
        holiday.description = form.description.data
        holiday.region_code = form.region_code.data
        holiday.holiday_type = form.holiday_type.data

        try:
            db.session.commit()
            return {
                'success': True,
                'message': f'Holiday "{holiday.name}" updated successfully!'
            }
        except Exception as e:
            db.session.rollback()
            current_app.logger.error(f'Error updating holiday: {str(e)}')
            return {
                'success': False,
                'message': 'Error updating holiday. Please try again.'
            }

    # Form validation failed
    error_messages = {field: errors for field, errors in form.errors.items()}
    return {
        'success': False,
        'message': 'Please correct the errors below.',
        'errors': error_messages
    }


@admin_bp.route('/holidays/bulk-import/form')
@login_required
@admin_required
def bulk_import_form():
    """Display bulk import form in drawer."""
    form = BulkImportForm()
    return render_template('admin/holidays/partials/bulk_import_form.html',
                          form=form,
                          action_url=url_for('admin.bulk_import_holidays_drawer'),
                          form_title="Bulk Import Holidays")


@admin_bp.route('/holidays/drawer/bulk-import', methods=['POST'])
@login_required
@admin_required
@ajax_response(success_redirect='admin.holidays')
@log_activity('Bulk imported holidays', entity_type='Holiday', category=Activity.CATEGORY_DATA, method=Activity.METHOD_CREATE)
def bulk_import_holidays_drawer():
    """Bulk import holidays from CSV file via drawer form."""
    form = BulkImportForm()

    if form.validate_on_submit():
        # Process the bulk import (reuse existing logic)
        try:
            # This would contain the same logic as the existing bulk_import_holidays function
            # For now, return a placeholder message
            return {
                'success': True,
                'message': 'Bulk import functionality will be implemented'
            }
        except Exception as e:
            current_app.logger.error(f'Error during bulk import: {str(e)}')
            return {
                'success': False,
                'message': 'Error processing bulk import. Please try again.'
            }

    # Form validation failed
    error_messages = {field: errors for field, errors in form.errors.items()}
    return {
        'success': False,
        'message': 'Please correct the errors below.',
        'errors': error_messages
    }
