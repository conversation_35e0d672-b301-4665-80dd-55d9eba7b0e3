"""
User import service for bulk importing users from CSV/XLSX files.
"""

import csv
import io
import pandas as pd
from datetime import datetime, date
from typing import Dict, List, Tuple, Any, Optional
from werkzeug.datastructures import FileStorage
from flask import current_app

from app import db
from app.models import User, EmployeeDetail, BusinessUnit, BusinessSegment
from app.models import PH_TZ


class UserImportService:
    """Service for importing users from CSV/XLSX files."""

    # Required columns for user import
    REQUIRED_COLUMNS = ['name', 'email']

    # Optional columns that map to User model
    USER_COLUMNS = {
        'name': 'name',
        'email': 'email',
        'role': 'role',
        'password': 'password',
        'is_active': 'is_active'
    }

    # Optional columns that map to EmployeeDetail model
    EMPLOYEE_COLUMNS = {
        'employee_number': 'employee_number',
        'first_name': 'first_name',
        'middle_name': 'middle_name',
        'last_name': 'last_name',
        'legal_name': 'legal_name',
        'job_title': 'job_title',
        'phone': 'phone',
        'emp_type': 'emp_type',
        'enterprise_id': 'enterprise_id',
        'manager_name': 'manager_name',
        'job_code': 'job_code',
        'manager_level': 'manager_level',
        'job_code_track_level': 'job_code_track_level',
        'business_unit_code': 'business_unit_id',
        'business_segment_code': 'business_segment_id',
        'hire_date': 'hire_date',
        'emp_status': 'emp_status'
    }

    def __init__(self):
        self.errors = []
        self.warnings = []
        self.created_count = 0
        self.updated_count = 0
        self.skipped_count = 0
        self.error_count = 0

    def import_users(self, file: FileStorage, default_role: str = 'User',
                    handle_duplicates: str = 'skip', default_password: str = '',
                    send_welcome_email: bool = False) -> Dict[str, Any]:
        """
        Import users from uploaded file.

        Args:
            file: Uploaded file (CSV or XLSX)
            default_role: Default role for users without role specified
            handle_duplicates: How to handle duplicate emails ('skip', 'update', 'error')
            default_password: Default password for users without password specified
            send_welcome_email: Whether to send welcome emails

        Returns:
            Dictionary with import results
        """
        try:
            # Reset counters
            self._reset_counters()

            # Parse file data
            data = self._parse_file(file)
            if not data:
                return self._create_error_result("Failed to parse file")

            # Validate data structure
            validation_result = self._validate_data_structure(data)
            if not validation_result['valid']:
                return self._create_error_result(validation_result['message'])

            # Cache business units and segments for lookup
            business_units = {bu.code: bu.id for bu in BusinessUnit.query.all()}
            business_segments = {bs.code: bs.id for bs in BusinessSegment.query.all()}

            # Process each row
            for row_num, row_data in enumerate(data, start=2):  # Start at 2 for Excel row numbering
                try:
                    self._process_user_row(
                        row_data, row_num, default_role, handle_duplicates,
                        default_password, business_units, business_segments
                    )
                except Exception as e:
                    self.error_count += 1
                    self.errors.append(f"Row {row_num}: {str(e)}")
                    if handle_duplicates == 'error':
                        db.session.rollback()
                        return self._create_error_result(f"Import stopped due to error in row {row_num}: {str(e)}")

            # Commit all changes if no critical errors
            if self.error_count == 0 or handle_duplicates != 'error':
                db.session.commit()
                return self._create_success_result()
            else:
                db.session.rollback()
                return self._create_error_result("Import failed due to errors")

        except Exception as e:
            db.session.rollback()
            current_app.logger.error(f"User import error: {str(e)}")
            return self._create_error_result(f"Import failed: {str(e)}")

    def _parse_file(self, file: FileStorage) -> Optional[List[Dict[str, Any]]]:
        """Parse CSV or XLSX file and return list of dictionaries."""
        try:
            filename = file.filename.lower()

            if filename.endswith('.csv'):
                return self._parse_csv(file)
            elif filename.endswith('.xlsx'):
                return self._parse_xlsx(file)
            else:
                self.errors.append("Unsupported file format. Please use CSV or XLSX.")
                return None

        except Exception as e:
            self.errors.append(f"Error parsing file: {str(e)}")
            return None

    def _parse_csv(self, file: FileStorage) -> List[Dict[str, Any]]:
        """Parse CSV file."""
        content = file.read().decode('utf-8-sig')  # Handle BOM
        file.seek(0)  # Reset file pointer

        csv_reader = csv.DictReader(io.StringIO(content))
        return [row for row in csv_reader]

    def _parse_xlsx(self, file: FileStorage) -> List[Dict[str, Any]]:
        """Parse XLSX file."""
        df = pd.read_excel(file, engine='openpyxl')
        # Convert NaN to None and handle data types
        df = df.where(pd.notnull(df), None)
        return df.to_dict('records')

    def _validate_data_structure(self, data: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Validate that required columns are present."""
        if not data:
            return {'valid': False, 'message': 'File is empty or has no data rows'}

        # Get column names (handle case variations)
        columns = [col.lower().strip() for col in data[0].keys()]

        # Check for required columns
        missing_columns = []
        for required_col in self.REQUIRED_COLUMNS:
            if required_col.lower() not in columns:
                missing_columns.append(required_col)

        if missing_columns:
            return {
                'valid': False,
                'message': f"Missing required columns: {', '.join(missing_columns)}"
            }

        return {'valid': True, 'message': 'Data structure is valid'}

    def _normalize_column_names(self, row_data: Dict[str, Any]) -> Dict[str, Any]:
        """Normalize column names to lowercase and strip whitespace."""
        normalized = {}
        for key, value in row_data.items():
            if key is not None:
                # Convert key to lowercase and strip whitespace
                normalized_key = str(key).lower().strip()
                # Only include non-empty values
                if value is not None and str(value).strip():
                    normalized[normalized_key] = value
        return normalized

    def _reset_counters(self):
        """Reset all counters and error lists."""
        self.errors = []
        self.warnings = []
        self.created_count = 0
        self.updated_count = 0
        self.skipped_count = 0
        self.error_count = 0

    def _process_user_row(self, row_data: Dict[str, Any], row_num: int,
                         default_role: str, handle_duplicates: str,
                         default_password: str, business_units: Dict[str, int],
                         business_segments: Dict[str, int]):
        """Process a single user row from the import data."""

        # Normalize column names (case insensitive)
        normalized_row = self._normalize_column_names(row_data)

        # Extract required fields
        name = normalized_row.get('name', '').strip()
        email = normalized_row.get('email', '').strip().lower()

        if not name or not email:
            raise ValueError("Name and email are required")

        # Validate email format (basic validation)
        if '@' not in email or '.' not in email.split('@')[1]:
            raise ValueError(f"Invalid email format: {email}")

        # Check for existing user
        existing_user = User.query.filter_by(email=email).first()

        if existing_user:
            if handle_duplicates == 'skip':
                self.skipped_count += 1
                self.warnings.append(f"Row {row_num}: Skipped existing user {email}")
                return
            elif handle_duplicates == 'error':
                raise ValueError(f"User with email {email} already exists")
            elif handle_duplicates == 'update':
                self._update_existing_user(existing_user, normalized_row, row_num,
                                         default_role, business_units, business_segments)
                return

        # Create new user
        self._create_new_user(normalized_row, row_num, default_role,
                            default_password, business_units, business_segments)

    def _create_new_user(self, row_data: Dict[str, Any], row_num: int,
                        default_role: str, default_password: str,
                        business_units: Dict[str, int], business_segments: Dict[str, int]):
        """Create a new user and associated employee detail."""

        # Extract user data
        user_data = self._extract_user_data(row_data, default_role, default_password)

        # Create user
        user = User(
            name=user_data['name'],  # type: ignore
            email=user_data['email'],  # type: ignore
            role=user_data['role'],  # type: ignore
        )
        user.is_active = user_data.get('is_active', True)

        # Set password
        if user_data.get('password'):
            user.set_password(user_data['password'])
        else:
            user.set_password(default_password)

        db.session.add(user)
        db.session.flush()  # Get user ID

        # Create employee detail if we have employee data
        employee_data = self._extract_employee_data(row_data, business_units, business_segments)
        if employee_data:
            employee_detail = EmployeeDetail(
                user_id=user.id,
                **employee_data
            )
            db.session.add(employee_detail)

        self.created_count += 1

    def _update_existing_user(self, user: User, row_data: Dict[str, Any], row_num: int,
                            default_role: str, business_units: Dict[str, int],
                            business_segments: Dict[str, int]):
        """Update an existing user and associated employee detail."""

        # Extract user data
        user_data = self._extract_user_data(row_data, default_role, '')

        # Update user fields
        if user_data.get('name'):
            user.name = user_data['name']
        if user_data.get('role'):
            user.role = user_data['role']
        if 'is_active' in user_data:
            user.is_active = user_data['is_active']

        # Update password if provided
        if user_data.get('password'):
            user.set_password(user_data['password'])

        # Update or create employee detail
        employee_data = self._extract_employee_data(row_data, business_units, business_segments)
        if employee_data:
            if user.employee_detail:
                # Update existing employee detail
                for key, value in employee_data.items():
                    if value is not None:
                        setattr(user.employee_detail, key, value)
            else:
                # Create new employee detail
                employee_detail = EmployeeDetail(
                    user_id=user.id,
                    **employee_data
                )
                db.session.add(employee_detail)

        self.updated_count += 1

    def _extract_user_data(self, row_data: Dict[str, Any], default_role: str,
                          default_password: str) -> Dict[str, Any]:
        """Extract user-related data from row."""
        user_data = {}

        for csv_col, model_field in self.USER_COLUMNS.items():
            value = row_data.get(csv_col)
            if value is not None and str(value).strip():
                if model_field == 'is_active':
                    # Convert to boolean
                    user_data[model_field] = str(value).lower() in ['true', '1', 'yes', 'active']
                else:
                    user_data[model_field] = str(value).strip()

        # Set defaults
        if 'role' not in user_data:
            user_data['role'] = default_role

        return user_data

    def _extract_employee_data(self, row_data: Dict[str, Any],
                              business_units: Dict[str, int],
                              business_segments: Dict[str, int]) -> Dict[str, Any]:
        """Extract employee-related data from row."""
        employee_data = {}

        for csv_col, model_field in self.EMPLOYEE_COLUMNS.items():
            value = row_data.get(csv_col)
            if value is not None and str(value).strip():
                if model_field == 'business_unit_id':
                    # Look up business unit by code
                    bu_id = business_units.get(str(value).strip())
                    if bu_id:
                        employee_data[model_field] = bu_id
                elif model_field == 'business_segment_id':
                    # Look up business segment by code
                    bs_id = business_segments.get(str(value).strip())
                    if bs_id:
                        employee_data[model_field] = bs_id
                elif model_field == 'hire_date':
                    # Parse date
                    try:
                        if isinstance(value, str):
                            # Try different date formats
                            for fmt in ['%Y-%m-%d', '%m/%d/%Y', '%d/%m/%Y']:
                                try:
                                    employee_data[model_field] = datetime.strptime(value.strip(), fmt).date()
                                    break
                                except ValueError:
                                    continue
                        elif hasattr(value, 'date'):  # pandas datetime
                            employee_data[model_field] = value.date()
                    except (ValueError, AttributeError):
                        pass  # Skip invalid dates
                else:
                    employee_data[model_field] = str(value).strip()

        return employee_data

    def _create_success_result(self) -> Dict[str, Any]:
        """Create success result dictionary."""
        message_parts = []
        if self.created_count > 0:
            message_parts.append(f"Created: {self.created_count}")
        if self.updated_count > 0:
            message_parts.append(f"Updated: {self.updated_count}")
        if self.skipped_count > 0:
            message_parts.append(f"Skipped: {self.skipped_count}")

        message = f"Import completed successfully! {', '.join(message_parts)}"

        return {
            'success': True,
            'message': message,
            'details': {
                'created': self.created_count,
                'updated': self.updated_count,
                'skipped': self.skipped_count,
                'errors': self.error_count,
                'warnings': self.warnings[:10],  # Limit warnings shown
                'error_messages': self.errors[:10]  # Limit errors shown
            }
        }

    def _create_error_result(self, message: str) -> Dict[str, Any]:
        """Create error result dictionary."""
        return {
            'success': False,
            'message': message,
            'details': {
                'created': self.created_count,
                'updated': self.updated_count,
                'skipped': self.skipped_count,
                'errors': self.error_count,
                'warnings': self.warnings[:10],
                'error_messages': self.errors[:10]
            }
        }
