"""
Forms for user administration.
"""

from flask_wtf import FlaskForm
from flask_wtf.file import <PERSON>Field, FileAllowed
from wtforms import StringField, SelectField, SubmitField, TextAreaField
from wtforms.validators import DataRequired, Length, Optional, ValidationError
from datetime import date

from app.models import User


class BulkUserImportForm(FlaskForm):
    """Form for bulk importing users from CSV or XLSX."""

    file = FileField(
        'Import File',
        validators=[
            DataRequired(message="Please select a CSV or XLSX file."),
            FileAllowed(['csv', 'xlsx'], 'Only CSV and XLSX files are allowed.')
        ]
    )

    default_role = SelectField(
        'Default Role',
        choices=[
            ('User', 'User'),
            ('Manager', 'Manager'),
            ('Admin', 'Admin')
        ],
        validators=[DataRequired(message="Default role is required.")],
        default='User',
        description="This role will be used for users that don't specify a role in the file."
    )

    handle_duplicates = SelectField(
        'Handle Duplicate Emails',
        choices=[
            ('skip', 'Skip existing users'),
            ('update', 'Update existing users'),
            ('error', 'Stop on conflicts')
        ],
        default='skip',
        description="How to handle users with email addresses that already exist."
    )

    default_password = StringField(
        'Default Password',
        validators=[
            DataRequired(message="Default password is required."),
            Length(min=8, message="Password must be at least 8 characters long.")
        ],
        description="Password to assign to users who don't have one specified in the file.",
        render_kw={"type": "password", "placeholder": "Enter default password"}
    )

    send_welcome_email = SelectField(
        'Send Welcome Email',
        choices=[
            ('no', 'No welcome email'),
            ('yes', 'Send welcome email to new users')
        ],
        default='no',
        description="Whether to send welcome emails to newly created users."
    )

    submit = SubmitField('Import Users')


class UserFilterForm(FlaskForm):
    """Form for filtering users in the admin list."""

    role = SelectField(
        'Role',
        choices=[
            ('', 'All Roles'),
            ('User', 'User'),
            ('Manager', 'Manager'),
            ('Admin', 'Admin')
        ],
        default=''
    )

    status = SelectField(
        'Status',
        choices=[
            ('', 'All Statuses'),
            ('active', 'Active'),
            ('inactive', 'Inactive')
        ],
        default=''
    )

    search = StringField(
        'Search',
        validators=[Optional(), Length(max=100)],
        render_kw={"placeholder": "Search by name or email..."}
    )

    submit = SubmitField('Apply Filters')
