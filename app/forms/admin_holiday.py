"""
Forms for holiday administration.
"""

from flask_wtf import FlaskForm
from flask_wtf.file import FileField, FileAllowed
from wtforms import StringField, DateField, TextAreaField, SelectField, SubmitField
from wtforms.validators import DataRequired, Length, Optional, ValidationError
from datetime import date

from app.models.attendance import Holiday


class HolidayForm(FlaskForm):
    """Form for creating and editing holidays."""

    name = StringField(
        'Holiday Name',
        validators=[
            DataRequired(message="Holiday name is required."),
            Length(min=2, max=100, message="Holiday name must be between 2 and 100 characters.")
        ],
        render_kw={"placeholder": "e.g., Christmas Day, Independence Day"}
    )

    date = DateField(
        'Date',
        validators=[DataRequired(message="Date is required.")],
        format='%Y-%m-%d'
    )

    region_code = SelectField(
        'Region',
        choices=[
            ('GLOBAL', 'Global'),
            ('US', 'United States'),
            ('PH', 'Philippines')
        ],
        validators=[DataRequired(message="Region is required.")],
        default='GLOBAL'
    )

    holiday_type = SelectField(
        'Holiday Type',
        choices=[
            ('regular', 'Regular Holiday'),
            ('special-non-working', 'Special Non-Working Holiday'),
            ('special-working', 'Special Working Holiday'),
            ('federal', 'Federal Holiday (US)')
        ],
        validators=[DataRequired(message="Holiday type is required.")],
        default='regular'
    )

    description = TextAreaField(
        'Description',
        validators=[Optional(), Length(max=500, message="Description cannot exceed 500 characters.")],
        render_kw={"rows": 3, "placeholder": "Optional description of the holiday."}
    )

    submit = SubmitField('Save Holiday')

    # Store the original values when editing to check for changes
    original_date = None
    original_region = None

    def __init__(self, original_date=None, original_region=None, *args, **kwargs):
        super(HolidayForm, self).__init__(*args, **kwargs)
        if original_date:
            self.original_date = original_date
        if original_region:
            self.original_region = original_region

    def validate_date(self, date_field):
        """Validate that the date and region combination is unique."""
        # If both date and region haven't changed from the original, it's valid (for edits)
        if (self.original_date and self.original_region and
            self.original_date == date_field.data and
            self.original_region == self.region_code.data):
            return

        # Check if the new date/region combination already exists in the database
        existing_holiday = Holiday.query.filter_by(
            date=date_field.data,
            region_code=self.region_code.data
        ).first()
        if existing_holiday:
            raise ValidationError(f'A holiday already exists on {date_field.data} for {self.region_code.data} region.')




class HolidayFilterForm(FlaskForm):
    """Form for filtering holidays in the admin list."""

    region = SelectField(
        'Region',
        choices=[
            ('', 'All Regions'),
            ('GLOBAL', 'Global'),
            ('US', 'United States'),
            ('PH', 'Philippines')
        ],
        default=''
    )

    search = StringField(
        'Search',
        validators=[Optional(), Length(max=100)],
        render_kw={"placeholder": "Search by name or description..."}
    )

    start_date = DateField(
        'Start Date',
        validators=[Optional()],
        format='%Y-%m-%d'
    )

    end_date = DateField(
        'End Date',
        validators=[Optional()],
        format='%Y-%m-%d'
    )

    submit = SubmitField('Filter')

    def validate_end_date(self, end_date_field):
        """Validate that end date is after start date."""
        if self.start_date.data and end_date_field.data:
            if end_date_field.data < self.start_date.data:
                raise ValidationError('End date must be after start date.')


class BulkImportForm(FlaskForm):
    """Form for bulk importing holidays from CSV."""

    file = FileField(
        'CSV File',
        validators=[
            DataRequired(message="Please select a CSV file."),
            FileAllowed(['csv'], 'Only CSV files are allowed.')
        ]
    )

    region_code = SelectField(
        'Default Region',
        choices=[
            ('GLOBAL', 'Global'),
            ('US', 'United States'),
            ('PH', 'Philippines')
        ],
        validators=[DataRequired(message="Default region is required.")],
        default='GLOBAL',
        description="This region will be used for holidays that don't specify a region in the CSV."
    )

    overwrite_existing = SelectField(
        'Handle Existing Holidays',
        choices=[
            ('skip', 'Skip existing holidays'),
            ('update', 'Update existing holidays'),
            ('error', 'Stop on conflicts')
        ],
        default='skip',
        description="How to handle holidays that already exist for the same date and region."
    )

    submit = SubmitField('Import Holidays')


class CalendarFilterForm(FlaskForm):
    """Form for filtering the holiday calendar view."""

    year = SelectField(
        'Year',
        coerce=int,
        validators=[DataRequired()]
    )

    month = SelectField(
        'Month',
        choices=[
            (0, 'All Months'),
            (1, 'January'), (2, 'February'), (3, 'March'),
            (4, 'April'), (5, 'May'), (6, 'June'),
            (7, 'July'), (8, 'August'), (9, 'September'),
            (10, 'October'), (11, 'November'), (12, 'December')
        ],
        coerce=int,
        default=0
    )

    region_code = SelectField(
        'Region',
        choices=[
            ('GLOBAL', 'Global'),
            ('US', 'United States'),
            ('PH', 'Philippines')
        ],
        validators=[DataRequired()],
        default='PH'
    )

    submit = SubmitField('Update Calendar')

    def __init__(self, *args, **kwargs):
        super(CalendarFilterForm, self).__init__(*args, **kwargs)

        # Populate year choices (current year ± 5 years)
        current_year = date.today().year
        year_choices = [(year, str(year)) for year in range(current_year - 5, current_year + 6)]
        self.year.choices = year_choices
        self.year.default = current_year


class QuickAddHolidayForm(FlaskForm):
    """Simplified form for quickly adding holidays (e.g., from calendar view)."""

    name = StringField(
        'Holiday Name',
        validators=[
            DataRequired(message="Holiday name is required."),
            Length(min=2, max=100, message="Holiday name must be between 2 and 100 characters.")
        ]
    )

    date = DateField(
        'Date',
        validators=[DataRequired(message="Date is required.")],
        format='%Y-%m-%d'
    )

    region_code = SelectField(
        'Region',
        choices=[
            ('GLOBAL', 'Global'),
            ('US', 'United States'),
            ('PH', 'Philippines')
        ],
        validators=[DataRequired(message="Region is required.")],
        default='GLOBAL'
    )

    holiday_type = SelectField(
        'Holiday Type',
        choices=[
            ('regular', 'Regular Holiday'),
            ('special-non-working', 'Special Non-Working Holiday'),
            ('special-working', 'Special Working Holiday'),
            ('federal', 'Federal Holiday (US)')
        ],
        validators=[DataRequired(message="Holiday type is required.")],
        default='regular'
    )

    submit = SubmitField('Add Holiday')

    def validate_date(self, date_field):
        """Validate that the date doesn't conflict with existing holidays."""
        existing_holiday = Holiday.query.filter(
            Holiday.date == date_field.data,
            Holiday.region_code == self.region_code.data
        ).first()

        if existing_holiday:
            raise ValidationError(
                f'A holiday already exists for {date_field.data} in region {self.region_code.data}. '
                f'Existing holiday: "{existing_holiday.name}"'
            )
