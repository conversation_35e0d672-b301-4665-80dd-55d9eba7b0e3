/**
 * Users page JavaScript functionality
 * Handles user management features including bulk import
 */

document.addEventListener('DOMContentLoaded', function() {
    console.log('Users page JavaScript loaded');
    
    // Register forms with drawer manager
    registerUserForms();
    
    // Initialize any additional user-specific functionality
    initializeUserManagement();
});

/**
 * Register user forms with drawer manager
 */
function registerUserForms() {
    // Check if drawerManager exists
    if (!window.drawerManager) {
        console.error('DrawerManager not found');
        return;
    }

    // Register user form
    window.drawerManager.registerFormType('user', {
        createUrl: '/forms/user',
        editUrl: '/forms/user?id={id}',
        size: 'md',
        position: 'right'
    });

    // Register bulk import form
    window.drawerManager.registerFormType('user_bulk_import', {
        createUrl: '/admin/users/bulk-import/form',
        editUrl: '/admin/users/bulk-import/form',
        size: 'lg',
        position: 'right'
    });

    console.log('User forms registered with drawer manager');
}

/**
 * Initialize user management functionality
 */
function initializeUserManagement() {
    // Initialize delete confirmation
    initializeDeleteConfirmation();
    
    // Initialize any filters or search functionality
    initializeUserFilters();
}

/**
 * Initialize delete confirmation for users
 */
function initializeDeleteConfirmation() {
    // This function would be called by delete buttons
    window.confirmDeleteUser = function(userId, userName) {
        if (confirm(`Are you sure you want to delete user "${userName}"? This action cannot be undone.`)) {
            // Create a form to submit the delete request
            const form = document.createElement('form');
            form.method = 'POST';
            form.action = `/admin/users/${userId}/delete`;
            
            // Add CSRF token
            const csrfToken = document.querySelector('meta[name="csrf-token"]');
            if (csrfToken) {
                const csrfInput = document.createElement('input');
                csrfInput.type = 'hidden';
                csrfInput.name = 'csrf_token';
                csrfInput.value = csrfToken.getAttribute('content');
                form.appendChild(csrfInput);
            }
            
            document.body.appendChild(form);
            form.submit();
        }
    };
}

/**
 * Initialize user filters and search
 */
function initializeUserFilters() {
    // Add any filter functionality here
    // This could include role filters, status filters, search, etc.
    
    const searchInput = document.querySelector('input[name="search"]');
    if (searchInput) {
        // Add debounced search functionality
        let searchTimeout;
        searchInput.addEventListener('input', function() {
            clearTimeout(searchTimeout);
            searchTimeout = setTimeout(() => {
                // Implement search functionality
                console.log('Search for:', this.value);
            }, 300);
        });
    }
}

/**
 * Handle bulk import progress and feedback
 */
function handleBulkImportProgress(data) {
    if (data.success) {
        // Show success message with details
        let message = data.message;
        if (data.details) {
            const details = data.details;
            if (details.created > 0 || details.updated > 0 || details.skipped > 0) {
                message += `\n\nDetails:\n`;
                if (details.created > 0) message += `• Created: ${details.created}\n`;
                if (details.updated > 0) message += `• Updated: ${details.updated}\n`;
                if (details.skipped > 0) message += `• Skipped: ${details.skipped}\n`;
            }
            
            // Show warnings if any
            if (details.warnings && details.warnings.length > 0) {
                message += `\nWarnings:\n`;
                details.warnings.forEach(warning => {
                    message += `• ${warning}\n`;
                });
            }
        }
        
        if (typeof showToast === 'function') {
            showToast(message, { type: 'success', duration: 5000 });
        } else {
            alert(message);
        }
    } else {
        // Show error message
        let message = data.message;
        if (data.details && data.details.error_messages) {
            message += '\n\nErrors:\n';
            data.details.error_messages.forEach(error => {
                message += `• ${error}\n`;
            });
        }
        
        if (typeof showToast === 'function') {
            showToast(message, { type: 'error', duration: 8000 });
        } else {
            alert(message);
        }
    }
}

// Export functions for global access
window.registerUserForms = registerUserForms;
window.handleBulkImportProgress = handleBulkImportProgress;
