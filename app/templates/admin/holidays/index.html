{% extends "base.html" %}

{% from "components/page_header.html" import page_header %}
{% from "components/table_header.html" import table_header %}
{% from "components/simple_table.html" import simple_table %}
{% from "components/action_buttons.html" import action_buttons %}
{% from "components/button.html" import button, button_group, icon_button %}
{% from "partials/forms/base_form.html" import form_group %}

{% block title %}Holidays{% endblock %}

{% block header %}Holidays{% endblock %}

{% block content %}
<!-- Hidden fields to store URLs for JavaScript use -->
<input type="hidden" id="add-holiday-url" data-url="{{ url_for('admin.get_holiday_form_create') }}">
<input type="hidden" id="edit-holiday-base-url" data-url="{{ url_for('admin.get_holiday_form_edit', holiday_id=0) }}">
<input type="hidden" id="delete-holiday-base-url" data-url="{{ url_for('admin.delete_holiday', holiday_id=0) }}">
<input type="hidden" id="csrf-token" data-token="{{ csrf_token() }}">

<div class="space-y-6">
  {{ page_header(
    title="Holiday Management",
    button_text="Add Holiday",
    button_icon="plus",
    button_action="openAddHolidayForm()",
    description="Manage company holidays by region and track holiday schedules."
  ) }}

  <!-- Filter Section -->
  <div class="card">
    <div class="card-header">
      <h3 class="card-title">Filter Holidays</h3>
    </div>
    <div class="card-content">
      <form method="GET" class="space-y-4">
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <!-- Region Filter -->
          {% set region_options = [('', 'All Regions')] %}
          {% for region in available_regions %}
            {% if region == 'US' %}
              {% set _ = region_options.append((region, '🇺🇸 United States')) %}
            {% elif region == 'PH' %}
              {% set _ = region_options.append((region, '🇵🇭 Philippines')) %}
            {% elif region == 'GLOBAL' %}
              {% set _ = region_options.append((region, '🌍 Global')) %}
            {% else %}
              {% set _ = region_options.append((region, region)) %}
            {% endif %}
          {% endfor %}
          {{ form_group(
            label="Region",
            name="region",
            type="select",
            value=current_filters.region if current_filters.region else '',
            options=region_options
          ) }}

          <!-- Search Filter -->
          {{ form_group(
            label="Search",
            name="search",
            type="text",
            value=current_filters.search if current_filters.search else "",
            placeholder="Search by name or description..."
          ) }}

          <!-- Start Date Filter -->
          {{ form_group(
            label="Start Date",
            name="start_date",
            type="date",
            value=current_filters.start_date if current_filters.start_date else ""
          ) }}

          <!-- End Date Filter -->
          {{ form_group(
            label="End Date",
            name="end_date",
            type="date",
            value=current_filters.end_date if current_filters.end_date else ""
          ) }}
        </div>

        <!-- Filter Actions -->
        <div class="flex justify-end gap-2">
          {% set filter_buttons = [
            {"text": "Reset", "variant": "outline", "href": url_for('admin.holidays'), "icon": "rotate-ccw"},
            {"text": "Apply Filters", "variant": "primary", "type": "submit", "icon": "filter"}
          ] %}
          {{ button_group(filter_buttons) }}
        </div>
      </form>
    </div>
  </div>

  <!-- Quick Actions -->
  {% set quick_action_buttons = [
    {"text": "Calendar View", "variant": "outline", "href": url_for('admin.holiday_calendar'), "icon": "calendar"},
    {"text": "Bulk Import", "variant": "outline", "onclick": "drawerManager.openForm('holiday_bulk_import')", "icon": "upload"},
    {"text": "Export", "variant": "outline", "onclick": "exportHolidays()", "icon": "download"}
  ] %}
  {{ button_group(quick_action_buttons) }}

  <!-- Holidays Table -->
  <div class="card">
    {{ table_header(
      title="Holiday List",
      count=total_count,
      count_label="holidays"
    ) }}

    {% call simple_table(
      headers=[
        {'label': 'Date', 'sortable': True},
        {'label': 'Name'},
        {'label': 'Region'},
        {'label': 'Type'},
        {'label': 'Description'},
        {'label': 'Actions', 'align': 'right'}
      ],
      items=holidays,
      empty_icon="calendar",
      empty_title="No holidays found",
      empty_description="Create your first holiday or adjust your filters.",
      empty_button_text="Add Holiday",
      empty_button_icon="plus",
      empty_button_action="openAddHolidayForm()"
    ) %}
      {% for holiday in holidays %}
      <tr class="border-b border-border hover:bg-muted/30">
        <td class="px-4 py-3 text-sm font-medium">
          <div class="flex items-center">
            <i data-lucide="calendar" class="w-4 h-4 mr-2 text-muted-foreground"></i>
            {{ holiday.date.strftime('%Y-%m-%d') }}
            <span class="ml-2 text-xs text-muted-foreground">
              ({{ holiday.date.strftime('%A') }})
            </span>
          </div>
        </td>
        <td class="px-4 py-3 text-sm">
          <div class="font-medium">{{ holiday.name }}</div>
        </td>
        <td class="px-4 py-3 text-sm">
          <span class="badge {% if holiday.region_code == 'US' %}badge-info{% elif holiday.region_code == 'PH' %}badge-success{% elif holiday.region_code == 'GLOBAL' %}badge-secondary{% else %}badge-primary{% endif %}">
            {% if holiday.region_code == 'US' %}🇺🇸 United States
            {% elif holiday.region_code == 'PH' %}🇵🇭 Philippines
            {% elif holiday.region_code == 'GLOBAL' %}🌍 Global
            {% else %}{{ holiday.region_code }}{% endif %}
          </span>
        </td>
        <td class="px-4 py-3 text-sm">
          <span class="badge {% if holiday.holiday_type == 'federal' %}badge-info{% elif holiday.holiday_type == 'regular' %}badge-success{% elif holiday.holiday_type == 'special-non-working' %}badge-warning{% elif holiday.holiday_type == 'special-working' %}badge-secondary{% else %}badge-primary{% endif %}">
            {% if holiday.holiday_type == 'federal' %}🇺🇸 Federal
            {% elif holiday.holiday_type == 'regular' %}📅 Regular
            {% elif holiday.holiday_type == 'special-non-working' %}🚫 Special Non-Working
            {% elif holiday.holiday_type == 'special-working' %}💼 Special Working
            {% else %}{{ holiday.holiday_type | title }}{% endif %}
          </span>
        </td>
        <td class="px-4 py-3 text-sm text-muted-foreground">
          {{ holiday.description | truncate(80) if holiday.description else '-' }}
        </td>
        <td class="px-4 py-3 text-sm text-right">
          {% set escaped_name = holiday.name | replace("'", "\\'") | replace('"', '\\"') %}
          <div class="flex justify-end space-x-2">
            {{ icon_button("edit", variant="ghost", size="sm", onclick="openEditHolidayForm(" ~ holiday.id ~ ")", title="Edit Holiday") }}
            {{ icon_button("trash-2", variant="ghost", size="sm", onclick="confirmDeleteHoliday(" ~ holiday.id ~ ", '" ~ escaped_name ~ "')", title="Delete Holiday", class_extra="text-destructive hover:text-destructive") }}
          </div>
        </td>
      </tr>
      {% endfor %}
    {% endcall %}

    <!-- Pagination -->
    {% include "partials/pagination.html" %}
  </div>
</div>


{% endblock %}

{% block scripts %}
{{ super() }}
<script src="{{ url_for('static', filename='js/pages/holidays.js') }}"></script>
{% endblock %}
