/**
 * Page Loader Test Script
 *
 * Simple test to verify the page loader functionality
 */

// Test configuration
const tests = [
    {
        name: 'Basic Loader Show/Hide',
        run: async () => {
            showPageLoader('Testing basic functionality...');
            await new Promise(resolve => setTimeout(resolve, 1000));
            hidePageLoader();
            return true;
        }
    },
    {
        name: 'Text Update',
        run: async () => {
            showPageLoader('Initial text...');
            await new Promise(resolve => setTimeout(resolve, 500));
            updatePageLoaderText('Updated text...');
            await new Promise(resolve => setTimeout(resolve, 500));
            hidePageLoader();
            return true;
        }
    },
    {
        name: 'Status Check',
        run: async () => {
            const initialStatus = isPageLoaderActive();
            showPageLoader('Status test...');
            const activeStatus = isPageLoaderActive();
            hidePageLoader();
            const finalStatus = isPageLoaderActive();

            return !initialStatus && activeStatus && !finalStatus;
        }
    },
    {
        name: 'WithPageLoader Helper',
        run: async () => {
            const result = await withPageLoader(
                () => new Promise(resolve => setTimeout(() => resolve('success'), 500)),
                'Testing helper function...'
            );
            return result === 'success';
        }
    },
    {
        name: 'Automatic Navigation Duration (3 seconds)',
        run: async () => {
            const startTime = Date.now();
            showPageLoader('Testing 3-second automatic duration...');

            // Try to hide immediately
            setTimeout(() => hidePageLoader(), 100);

            // Wait for loader to actually hide
            return new Promise(resolve => {
                const checkHidden = () => {
                    if (!isPageLoaderActive()) {
                        const duration = Date.now() - startTime;
                        // Should take at least 3 seconds (2900ms to account for timing variations)
                        resolve(duration >= 2900);
                    } else {
                        setTimeout(checkHidden, 100);
                    }
                };
                checkHidden();
            });
        }
    },
    {
        name: 'Default Configuration Check',
        run: async () => {
            // Check that the default minimumDuration is 3000ms for automatic navigation
            const expectedDuration = 3000;
            const actualDuration = window.pageLoader ? window.pageLoader.minimumDuration : 0;
            return actualDuration === expectedDuration;
        }
    }
];

// Test runner
async function runTests() {
    console.log('🧪 Running Page Loader Tests...\n');

    let passed = 0;
    let failed = 0;

    for (const test of tests) {
        try {
            console.log(`⏳ Running: ${test.name}`);
            const result = await test.run();

            if (result) {
                console.log(`✅ PASSED: ${test.name}`);
                passed++;
            } else {
                console.log(`❌ FAILED: ${test.name}`);
                failed++;
            }
        } catch (error) {
            console.log(`❌ ERROR: ${test.name} - ${error.message}`);
            failed++;
        }

        // Wait between tests
        await new Promise(resolve => setTimeout(resolve, 200));
    }

    console.log(`\n📊 Test Results:`);
    console.log(`✅ Passed: ${passed}`);
    console.log(`❌ Failed: ${failed}`);
    console.log(`📝 Total: ${tests.length}`);

    if (failed === 0) {
        console.log('🎉 All tests passed!');
    } else {
        console.log('⚠️  Some tests failed.');
    }
}

// Visual test function
function runVisualTest() {
    console.log('🎨 Running Visual Test...');

    let step = 1;
    const totalSteps = 5;

    function nextStep() {
        switch (step) {
            case 1:
                showPageLoader('Visual Test - Step 1/5: Basic loader...');
                setTimeout(nextStep, 1500);
                break;
            case 2:
                updatePageLoaderText('Visual Test - Step 2/5: Text update...');
                setTimeout(nextStep, 1500);
                break;
            case 3:
                updatePageLoaderText('Visual Test - Step 3/5: Theme compatibility...');
                setTimeout(nextStep, 1500);
                break;
            case 4:
                updatePageLoaderText('Visual Test - Step 4/5: Animation test...');
                setTimeout(nextStep, 1500);
                break;
            case 5:
                updatePageLoaderText('Visual Test - Step 5/5: Completing...');
                setTimeout(() => {
                    hidePageLoader();
                    console.log('✅ Visual test completed!');
                }, 1500);
                break;
        }
        step++;
    }

    nextStep();
}

// Make functions available globally for testing
window.runPageLoaderTests = runTests;
window.runPageLoaderVisualTest = runVisualTest;

// Auto-run tests when loaded (if not in production)
if (window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1') {
    document.addEventListener('DOMContentLoaded', () => {
        // Wait for page loader to be ready
        setTimeout(() => {
            if (window.pageLoader && typeof showPageLoader === 'function') {
                console.log('📍 Page Loader Test Suite Loaded');
                console.log('🔧 Run runPageLoaderTests() for automated tests');
                console.log('🎨 Run runPageLoaderVisualTest() for visual test');
                console.log('📚 Check docs/components/page-loader.md for documentation');
            } else {
                console.error('❌ Page Loader not found - check implementation');
            }
        }, 1000);
    });
}
