/**
 * Page Loader JavaScript
 *
 * Handles showing and hiding the page loader during navigation
 */

class PageLoader {
  constructor() {
    this.loader = null;
    this.isActive = false;
    this.hideTimeout = null;
    this.minimumDuration = 3000; // 3-second automatic loading for all navigation
    this.startTime = null;

    this.init();
  }

  /**
   * Initialize the page loader
   */
  init() {
    // Create loader element
    this.createLoader();

    // Listen for page navigation events
    this.setupEventListeners();

    // Handle initial page load
    this.handleInitialLoad();
  }

  /**
   * Create the loader HTML element
   */
  createLoader() {
    // Remove any existing loader
    const existingLoader = document.getElementById('page-loader');
    if (existingLoader) {
      existingLoader.remove();
    }

    // Create loader container
    this.loader = document.createElement('div');
    this.loader.id = 'page-loader';
    this.loader.className = 'page-loader hidden';

    // Get the appropriate loader image based on theme
    const isDark = document.documentElement.classList.contains('dark');
    const loaderSrc = '/static/images/loader.svg';

    // Create loader content
    this.loader.innerHTML = `
      <div class="page-loader-content">
        <img
          src="${loaderSrc}"
          alt="Loading..."
          class="page-loader-spinner"
          loading="eager"
        >
        <div class="page-loader-text">
          Loading<span class="page-loader-dots"></span>
        </div>
        <div class="page-loader-progress">
          <div class="page-loader-progress-bar"></div>
        </div>
      </div>
    `;

    // Add to document
    document.body.appendChild(this.loader);
  }

  /**
   * Setup event listeners for navigation
   */
  setupEventListeners() {
    // Enhanced automatic navigation detection
    this.setupLinkInterception();
    this.setupFormInterception();
    this.setupBrowserNavigation();
    this.setupPageVisibility();
    this.setupThemeChanges();
  }

  /**
   * Setup comprehensive link click interception
   */
  setupLinkInterception() {
    // Use capture phase to catch all link clicks before they bubble
    document.addEventListener('click', (event) => {
      const target = event.target.closest('a');
      if (target && this.shouldShowLoader(target)) {
        const href = target.href;

        // Only handle actual navigation links
        if (href && href !== '#' && !href.startsWith('#')) {
          // Prevent immediate navigation
          event.preventDefault();
          event.stopImmediatePropagation();

          // Show loader first
          this.show('Navigating...');

          // Force a longer delay to ensure loader is visible and rendered
          setTimeout(() => {
            // Double-check the loader is still active before navigating
            if (this.isActive) {
              window.location.href = href;
            }
          }, 200); // Increased delay to ensure loader renders
        }
      }
    }, true); // Use capture phase
  }

  /**
   * Setup form submission interception
   */
  setupFormInterception() {
    document.addEventListener('submit', (event) => {
      const form = event.target;
      if (form && this.shouldShowLoaderForForm(form)) {
        this.show('Submitting...');
      }
    });
  }

  /**
   * Setup browser navigation (back/forward buttons)
   */
  setupBrowserNavigation() {
    // Listen for browser navigation (back/forward)
    window.addEventListener('popstate', () => {
      this.show('Loading page...');
    });

    // Listen for programmatic navigation
    const originalPushState = history.pushState;
    const originalReplaceState = history.replaceState;

    history.pushState = (...args) => {
      this.show('Loading page...');
      return originalPushState.apply(history, args);
    };

    history.replaceState = (...args) => {
      this.show('Loading page...');
      return originalReplaceState.apply(history, args);
    };
  }

  /**
   * Setup page visibility change handling
   */
  setupPageVisibility() {
    document.addEventListener('visibilitychange', () => {
      if (document.visibilityState === 'visible' && this.isActive) {
        // Page became visible again, but don't hide immediately
        // Let the minimum duration complete first
        const elapsed = this.startTime ? Date.now() - this.startTime : 0;
        const remaining = Math.max(0, this.minimumDuration - elapsed);

        if (remaining > 0) {
          // Still within minimum duration, let it complete normally
          setTimeout(() => {
            if (this.isActive) {
              this.hide();
            }
          }, remaining);
        } else {
          // Minimum duration has passed, safe to hide
          setTimeout(() => this.hide(), 100);
        }
      }
    });

    // Auto-hide on page unload, but don't interfere with minimum duration
    window.addEventListener('beforeunload', () => {
      // Only force hide if we're beyond the minimum duration
      const elapsed = this.startTime ? Date.now() - this.startTime : 0;
      if (elapsed >= this.minimumDuration) {
        this.forceHide();
      }
    });
  }

  /**
   * Setup theme change detection
   */
  setupThemeChanges() {
    // Listen for theme changes to update loader image
    const observer = new MutationObserver((mutations) => {
      mutations.forEach((mutation) => {
        if (mutation.attributeName === 'class') {
          this.updateLoaderForTheme();
        }
      });
    });
    observer.observe(document.documentElement, { attributes: true });
  }

  /**
   * Handle initial page load
   */
  handleInitialLoad() {
    // Show loader if page is still loading
    if (document.readyState === 'loading') {
      this.show('Loading page...');

      // Hide loader when page is fully loaded
      const hideLoader = () => {
        setTimeout(() => this.hide(), 200);
      };

      if (document.readyState === 'complete') {
        hideLoader();
      } else {
        window.addEventListener('load', hideLoader, { once: true });
      }
    }
  }

  /**
   * Check if loader should be shown for a link
   */
  shouldShowLoader(link) {
    // Don't show for external links
    if (link.hostname && link.hostname !== window.location.hostname) {
      return false;
    }

    // Don't show for hash links on same page
    if (link.getAttribute('href')?.startsWith('#')) {
      return false;
    }

    // Don't show for download links
    if (link.hasAttribute('download')) {
      return false;
    }

    // Don't show for links that open in new tab/window
    if (link.target === '_blank' || link.target === '_new') {
      return false;
    }

    // Don't show for mailto: or tel: links
    const href = link.getAttribute('href') || '';
    if (href.startsWith('mailto:') || href.startsWith('tel:')) {
      return false;
    }

    // Don't show if link has data-no-loader attribute
    if (link.hasAttribute('data-no-loader')) {
      return false;
    }

    return true;
  }

  /**
   * Check if loader should be shown for a form
   */
  shouldShowLoaderForForm(form) {
    // Don't show if form has data-no-loader attribute
    if (form.hasAttribute('data-no-loader')) {
      return false;
    }

    // Don't show for forms that submit via AJAX (if they have specific classes)
    if (form.classList.contains('ajax-form') || form.classList.contains('no-reload')) {
      return false;
    }

    return true;
  }

  /**
   * Update loader image based on current theme
   */
  updateLoaderForTheme() {
    if (!this.loader) return;

    const spinner = this.loader.querySelector('.page-loader-spinner');
    if (spinner) {
      const isDark = document.documentElement.classList.contains('dark');
      // Always use the main loader.svg as it should work for both themes
      spinner.src = '/static/images/loader.svg';
    }
  }

  /**
   * Show the page loader
   */
  show(text = 'Loading...') {
    if (this.isActive) return;

    this.isActive = true;
    this.startTime = Date.now();

    // Clear any existing hide timeout
    if (this.hideTimeout) {
      clearTimeout(this.hideTimeout);
      this.hideTimeout = null;
    }

    // Update text if provided
    const textElement = this.loader.querySelector('.page-loader-text');
    if (textElement && text) {
      textElement.innerHTML = `${text}<span class="page-loader-dots"></span>`;
    }

    // Show loader with forced reflow to ensure rendering
    this.loader.classList.remove('hidden');
    this.loader.offsetHeight; // Force reflow
    this.loader.classList.add('show');

    // Prevent scrolling
    document.body.style.overflow = 'hidden';

    // Ensure minimum duration is always respected
    this.hideTimeout = setTimeout(() => {
      if (this.isActive) {
        this.hide();
      }
    }, this.minimumDuration + 1000); // Safety net: minimum duration + 1 second
  }

  /**
   * Hide the page loader
   */
  hide() {
    if (!this.isActive) return;

    const now = Date.now();
    const elapsed = this.startTime ? now - this.startTime : 0;
    const remaining = Math.max(0, this.minimumDuration - elapsed);

    // Ensure loader shows for minimum duration
    if (remaining > 0) {
      setTimeout(() => this.hide(), remaining);
      return;
    }

    this.isActive = false;

    // Clear timeout
    if (this.hideTimeout) {
      clearTimeout(this.hideTimeout);
      this.hideTimeout = null;
    }

    // Hide loader
    this.loader.classList.add('hidden');
    this.loader.classList.remove('show');

    // Restore scrolling
    document.body.style.overflow = '';

    // Reset start time
    this.startTime = null;
  }

  /**
   * Force hide the loader (for emergency situations)
   */
  forceHide() {
    this.isActive = false;
    if (this.hideTimeout) {
      clearTimeout(this.hideTimeout);
      this.hideTimeout = null;
    }
    if (this.loader) {
      this.loader.classList.add('hidden');
      this.loader.classList.remove('show');
    }
    document.body.style.overflow = '';
    this.startTime = null;
  }

  /**
   * Check if loader is currently active
   */
  isLoading() {
    return this.isActive;
  }

  /**
   * Update loader text while it's showing
   */
  updateText(text) {
    if (!this.isActive || !this.loader) return;

    const textElement = this.loader.querySelector('.page-loader-text');
    if (textElement) {
      textElement.innerHTML = `${text}<span class="page-loader-dots"></span>`;
    }
  }
}

// Initialize page loader when DOM is ready
let pageLoader;

function initPageLoader() {
  pageLoader = new PageLoader();

  // Make it globally accessible for manual control
  window.pageLoader = pageLoader;
}

// Initialize immediately if DOM is ready, otherwise wait
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', initPageLoader);
} else {
  initPageLoader();
}

// Export for modules (if needed)
if (typeof module !== 'undefined' && module.exports) {
  module.exports = PageLoader;
}
