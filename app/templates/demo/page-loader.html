<!DOCTYPE html>
<html lang="en" class="h-full">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Page Loader Demo</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='css/main.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/page-loader.css') }}">
    <style>
        .demo-section {
            background: white;
            border-radius: 8px;
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }
        .dark .demo-section {
            background: hsl(var(--card));
            border: 1px solid hsl(var(--border));
        }
        .demo-button {
            background: hsl(var(--primary));
            color: hsl(var(--primary-foreground));
            padding: 0.75rem 1.5rem;
            border-radius: 6px;
            border: none;
            cursor: pointer;
            font-weight: 500;
            margin: 0.5rem;
            transition: all 0.2s;
        }
        .demo-button:hover {
            opacity: 0.9;
            transform: translateY(-1px);
        }
        .demo-button:active {
            transform: translateY(0);
        }
        .code-block {
            background: hsl(var(--muted));
            border: 1px solid hsl(var(--border));
            border-radius: 6px;
            padding: 1rem;
            margin: 1rem 0;
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
            font-size: 0.875rem;
            overflow-x: auto;
        }
    </style>
</head>
<body class="h-full bg-background text-foreground">
    <div class="container mx-auto px-4 py-8">
        <h1 class="text-3xl font-bold mb-8 text-center">Page Loader Demo</h1>

        <!-- Basic Usage -->
        <div class="demo-section">
            <h2 class="text-2xl font-semibold mb-4">Basic Usage</h2>
            <p class="mb-4 text-muted-foreground">
                Click these buttons to see the page loader in action with different text and durations.
            </p>
            <button class="demo-button" onclick="showBasicLoader()">Show Basic Loader</button>
            <button class="demo-button" onclick="showCustomLoader()">Custom Text Loader</button>
            <button class="demo-button" onclick="showTimedLoader()">Timed Loader (3s)</button>

            <div class="code-block">
                <strong>JavaScript Examples:</strong><br>
                <code>showPageLoader(); // Basic loader</code><br>
                <code>showPageLoader('Custom message...'); // With custom text</code><br>
                <code>setTimeout(hidePageLoader, 3000); // Hide after 3 seconds</code>
            </div>
        </div>

        <!-- Form Integration -->
        <div class="demo-section">
            <h2 class="text-2xl font-semibold mb-4">Form Integration</h2>
            <p class="mb-4 text-muted-foreground">
                Demonstrates how the loader integrates with form submissions.
            </p>
            <form id="demo-form" data-no-loader="true" onsubmit="handleFormSubmit(event)">
                <input type="text" placeholder="Enter some text..." class="p-2 border rounded mr-2 bg-background text-foreground border-border">
                <button type="submit" class="demo-button">Submit with Loader</button>
            </form>

            <div class="code-block">
                <strong>Form Integration:</strong><br>
                <code>showLoaderForForm(form, 'Submitting...');</code><br>
                <code>// Process form...</code><br>
                <code>hideLoaderForForm(form);</code>
            </div>
        </div>

        <!-- AJAX Integration -->
        <div class="demo-section">
            <h2 class="text-2xl font-semibold mb-4">AJAX Integration</h2>
            <p class="mb-4 text-muted-foreground">
                Shows how to use the loader with asynchronous operations.
            </p>
            <button class="demo-button" onclick="simulateAjaxRequest()">Simulate AJAX Request</button>
            <button class="demo-button" onclick="useWithPageLoader()">Use withPageLoader Helper</button>

            <div class="code-block">
                <strong>AJAX Examples:</strong><br>
                <code>showLoaderForAjax(fetch('/api/data'), 'Loading data...');</code><br>
                <code>withPageLoader(async () => { /* your async code */ }, 'Processing...');</code>
            </div>
        </div>

        <!-- Navigation Integration -->
        <div class="demo-section">
            <h2 class="text-2xl font-semibold mb-4">Navigation Integration</h2>
            <p class="mb-4 text-muted-foreground">
                The loader automatically shows when navigating between pages. Try these links:
            </p>
            <a href="{{ url_for('admin.dashboard' if current_user.is_admin else 'main.user_dashboard') }}" class="demo-button inline-block">Go to Dashboard</a>
            <a href="#" onclick="simulateNavigation(); return false;" class="demo-button inline-block">Simulate Navigation</a>

            <div class="code-block">
                <strong>Automatic Navigation:</strong><br>
                The loader automatically shows for:<br>
                • Link clicks (unless they have <code>data-no-loader</code> attribute)<br>
                • Form submissions (unless they have <code>data-no-loader</code> attribute)<br>
                • Browser back/forward navigation
            </div>
        </div>

        <!-- Manual Control -->
        <div class="demo-section">
            <h2 class="text-2xl font-semibold mb-4">Manual Control</h2>
            <p class="mb-4 text-muted-foreground">
                Complete control over the loader state and text updates.
            </p>
            <button class="demo-button" onclick="showUpdatingLoader()">Show with Text Updates</button>
            <button class="demo-button" onclick="forceHidePageLoader()">Force Hide</button>
            <button class="demo-button" onclick="checkLoaderStatus()">Check Status</button>

            <div class="code-block">
                <strong>Manual Control:</strong><br>
                <code>showPageLoader('Initial text...');</code><br>
                <code>updatePageLoaderText('Updated text...');</code><br>
                <code>isPageLoaderActive(); // returns boolean</code><br>
                <code>forceHidePageLoader(); // emergency hide</code>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="{{ url_for('static', filename='js/components/page-loader.js') }}"></script>
    <script src="{{ url_for('static', filename='js/utils/page-loader-utils.js') }}"></script>

    <script>
        // Demo functions
        function showBasicLoader() {
            showPageLoader();
            setTimeout(hidePageLoader, 2000);
        }

        function showCustomLoader() {
            showPageLoader('Loading custom content...');
            setTimeout(hidePageLoader, 2500);
        }

        function showTimedLoader() {
            showPageLoader('This will disappear in 3 seconds...');
            setTimeout(hidePageLoader, 3000);
        }

        function handleFormSubmit(event) {
            event.preventDefault();
            const form = event.target;
            showLoaderForForm(form, 'Processing form...');

            // Simulate form processing
            setTimeout(() => {
                hideLoaderForForm(form);
                alert('Form submitted successfully!');
            }, 2000);
        }

        function simulateAjaxRequest() {
            showPageLoader('Making AJAX request...');

            // Simulate an API call
            setTimeout(() => {
                updatePageLoaderText('Processing response...');
                setTimeout(() => {
                    hidePageLoader();
                    alert('AJAX request completed!');
                }, 1000);
            }, 1500);
        }

        async function useWithPageLoader() {
            try {
                const result = await withPageLoader(
                    new Promise(resolve => setTimeout(() => resolve('Success!'), 2000)),
                    'Using withPageLoader helper...'
                );
                alert(`Result: ${result}`);
            } catch (error) {
                alert(`Error: ${error.message}`);
            }
        }

        function simulateNavigation() {
            showPageLoader('Navigating to new page...');
            setTimeout(() => {
                // In a real scenario, this would be window.location.href = 'new-url'
                hidePageLoader();
                alert('Navigation completed!');
            }, 1500);
        }

        function showUpdatingLoader() {
            showPageLoader('Step 1: Initializing...');

            setTimeout(() => updatePageLoaderText('Step 2: Loading data...'), 1000);
            setTimeout(() => updatePageLoaderText('Step 3: Processing...'), 2000);
            setTimeout(() => updatePageLoaderText('Step 4: Finalizing...'), 3000);
            setTimeout(() => hidePageLoader(), 4000);
        }

        function checkLoaderStatus() {
            const isActive = isPageLoaderActive();
            alert(`Loader is currently: ${isActive ? 'ACTIVE' : 'INACTIVE'}`);
        }
    </script>
</body>
</html>
