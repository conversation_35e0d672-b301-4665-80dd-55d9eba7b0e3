{# This form partial is loaded into a drawer by client-side JavaScript #}
{# The `action_url` and `form_title` are passed from the route #}
{% from "partials/forms/base_form.html" import form_group, form_actions, form_header %}

<form method="POST" action="{{ action_url }}" id="holidayDrawerForm" class="space-y-4">
  {{ form.hidden_tag() }}

  {{ form_header(
    title = form_title,
    description = "Manage company holidays by region and track holiday schedules"
  ) }}

  <div class="grid grid-cols-1 gap-4">
    {{ form_group(
      label = form.name.label.text,
      name = form.name.name,
      value = form.name.data if form.name.data else "",
      required = form.name.flags.required,
      placeholder = "e.g., Christmas Day, Independence Day"
    ) }}
    {% if form.name.errors %}
      <div class="text-xs text-destructive mt-1">
        <ul>
          {% for error in form.name.errors %}<li>{{ error }}</li>{% endfor %}
        </ul>
      </div>
    {% endif %}

    {{ form_group(
      label = form.date.label.text,
      name = form.date.name,
      type = "date",
      value = form.date.data if form.date.data else "",
      required = form.date.flags.required
    ) }}
    {% if form.date.errors %}
      <div class="text-xs text-destructive mt-1">
        <ul>
          {% for error in form.date.errors %}<li>{{ error }}</li>{% endfor %}
        </ul>
      </div>
    {% endif %}

    {{ form_group(
      label = form.region_code.label.text,
      name = form.region_code.name,
      type = "select",
      value = form.region_code.data if form.region_code.data else "",
      required = form.region_code.flags.required,
      options = form.region_code.choices
    ) }}
    {% if form.region_code.errors %}
      <div class="text-xs text-destructive mt-1">
        <ul>
          {% for error in form.region_code.errors %}<li>{{ error }}</li>{% endfor %}
        </ul>
      </div>
    {% endif %}

    {{ form_group(
      label = form.holiday_type.label.text,
      name = form.holiday_type.name,
      type = "select",
      value = form.holiday_type.data if form.holiday_type.data else "",
      required = form.holiday_type.flags.required,
      options = form.holiday_type.choices
    ) }}
    {% if form.holiday_type.errors %}
      <div class="text-xs text-destructive mt-1">
        <ul>
          {% for error in form.holiday_type.errors %}<li>{{ error }}</li>{% endfor %}
        </ul>
      </div>
    {% endif %}

    {{ form_group(
      label = form.description.label.text,
      name = form.description.name,
      type = "textarea",
      value = form.description.data if form.description.data else "",
      placeholder = "Optional description of the holiday"
    ) }}
    {% if form.description.errors %}
      <div class="text-xs text-destructive mt-1">
        <ul>
          {% for error in form.description.errors %}<li>{{ error }}</li>{% endfor %}
        </ul>
      </div>
    {% endif %}
  </div>

  <div class="flex justify-end space-x-2 pt-4">
    <button type="button" class="btn btn-outline btn-md drawer-close">Cancel</button>
    <button type="button" onclick="submitDrawerForm('holidayDrawerForm')" class="btn btn-primary btn-md">
      {{ form.submit.label.text }}
    </button>
  </div>
</form>
