"""
Admin user management routes.
"""

from flask import render_template, redirect, url_for, flash, request, jsonify, current_app
from flask_login import login_required, current_user

from app import db
from app.models import User, BusinessUnit, BusinessSegment, Activity
from app.utils.decorators import admin_required, log_activity
from app.utils.cache_helpers import invalidate_dashboard_cache, invalidate_employee_cache
from app.utils.pagination import paginate_query
from app.utils.ajax_helpers import ajax_response
from app.routes.admin import admin_bp
from app.forms.admin_user import BulkUserImportForm
from app.services.user_import_service import UserImportService


@admin_bp.route('/users')
@login_required
@admin_required
@log_activity('Viewed users list', entity_type='User', category=Activity.CATEGORY_ADMIN, method=Activity.METHOD_READ)
def users():
    """Display paginated list of users."""
    # Build the query for users
    query = User.query

    # Use admin per_page from config
    per_page = current_app.config.get('PAGINATION_PER_PAGE_ADMIN', 20)

    # Use centralized pagination
    users, pagination = paginate_query(query, per_page=per_page)

    # Get business units and segments for forms
    business_units = BusinessUnit.query.all()
    business_segments = BusinessSegment.query.all()

    return render_template('admin/users.html',
                          users=users,
                          business_units=business_units,
                          business_segments=business_segments,
                          pagination=pagination,
                          active_page='users')


@admin_bp.route('/users/<int:user_id>')
@login_required
@admin_required
def get_user(user_id):
    """Get user details in JSON format."""
    user = User.query.get_or_404(user_id)
    return jsonify({
        'id': user.id,
        'name': user.name,
        'email': user.email,
        'role': user.role,
        'is_active': user.is_active
    })


@admin_bp.route('/users/create', methods=['POST'])
@login_required
@admin_required
@log_activity('Created user', entity_type='User', category=Activity.CATEGORY_DATA, method=Activity.METHOD_CREATE)
def create_user():
    """Create a new user."""
    name = request.form.get('name')
    email = request.form.get('email')
    password = request.form.get('password')
    role = request.form.get('role')

    # Validate input
    if not name or not email or not password or not role:
        flash('Required fields are missing', 'error')
        return redirect(url_for('admin.users'))

    # Check if user already exists
    existing_user = User.query.filter_by(email=email).first()
    if existing_user:
        flash('Email already registered', 'error')
        return redirect(url_for('admin.users'))

    # Create new user
    new_user = User(
        name=name,  # type: ignore
        email=email,  # type: ignore
        role=role,  # type: ignore
    )
    new_user.set_password(password)

    db.session.add(new_user)
    db.session.commit()

    # Invalidate cache
    current_app.logger.info("Invalidating dashboard cache after creating user")
    invalidate_dashboard_cache()
    invalidate_employee_cache()

    flash('User created successfully', 'success')
    return redirect(url_for('admin.users', status='success', action='created'))


@admin_bp.route('/users/<int:user_id>/update', methods=['POST'])
@login_required
@admin_required
@log_activity('Updated user', entity_type='User', category=Activity.CATEGORY_DATA, method=Activity.METHOD_UPDATE, track_changes=True)
def update_user(user_id):
    """Update an existing user."""
    user = User.query.get_or_404(user_id)

    name = request.form.get('name')
    email = request.form.get('email')
    role = request.form.get('role')
    is_active = True if request.form.get('is_active') else False
    new_password = request.form.get('new_password')

    # Validate input
    if not name or not email or not role:
        flash('Required fields are missing', 'error')
        return redirect(url_for('admin.users'))

    # Check if email is taken by another user
    existing_user = User.query.filter(User.email == email, User.id != user_id).first()
    if existing_user:
        flash('Email already registered to another user', 'error')
        return redirect(url_for('admin.users'))

    # Update user
    user.name = name
    user.email = email
    user.role = role
    user.is_active = is_active

    # Update password if provided
    if new_password:
        user.set_password(new_password)

    db.session.commit()

    # Invalidate cache
    current_app.logger.info("Invalidating dashboard cache after updating user")
    invalidate_dashboard_cache()
    invalidate_employee_cache()

    flash('User updated successfully', 'success')
    return redirect(url_for('admin.users', status='success', action='updated'))


@admin_bp.route('/users/<int:user_id>/delete', methods=['POST'])
@login_required
@admin_required
@log_activity('Deleted user', entity_type='User', category=Activity.CATEGORY_DATA, severity=Activity.SEVERITY_WARNING, method=Activity.METHOD_DELETE)
def delete_user(user_id):
    """Delete a user."""
    user = User.query.get_or_404(user_id)

    # Prevent deleting yourself
    if user.id == current_user.id:
        flash('You cannot delete your own account', 'error')
        return redirect(url_for('admin.users'))

    # Delete user
    db.session.delete(user)
    db.session.commit()

    # Invalidate cache
    current_app.logger.info("Invalidating dashboard cache after deleting user")
    invalidate_dashboard_cache()
    invalidate_employee_cache()

    flash('User deleted successfully', 'success')
    return redirect(url_for('admin.users', status='success', action='deleted'))


@admin_bp.route('/users/bulk-import/form')
@login_required
@admin_required
def user_bulk_import_form():
    """Display bulk import form in drawer."""
    form = BulkUserImportForm()
    return render_template('admin/users/partials/bulk_import_form.html',
                          form=form,
                          action_url=url_for('admin.bulk_import_users_drawer'),
                          form_title="Bulk Import Users")


@admin_bp.route('/users/drawer/bulk-import', methods=['POST'])
@login_required
@admin_required
@ajax_response(success_redirect='admin.users')
@log_activity('Bulk imported users', entity_type='User', category=Activity.CATEGORY_DATA, method=Activity.METHOD_CREATE)
def bulk_import_users_drawer():
    """Bulk import users from CSV/XLSX file via drawer form."""
    form = BulkUserImportForm()

    if form.validate_on_submit():
        try:
            # Initialize import service
            import_service = UserImportService()

            # Process the bulk import
            result = import_service.import_users(
                file=form.file.data,
                default_role=form.default_role.data or 'User',
                handle_duplicates=form.handle_duplicates.data or 'skip',
                default_password=form.default_password.data or 'TempPassword123!',
                send_welcome_email=form.send_welcome_email.data == 'yes'
            )

            # Invalidate cache if successful
            if result['success']:
                current_app.logger.info("Invalidating caches after bulk user import")
                invalidate_dashboard_cache()
                invalidate_employee_cache()

            return result

        except Exception as e:
            current_app.logger.error(f'Error during bulk user import: {str(e)}')
            return {
                'success': False,
                'message': f'Error processing bulk import: {str(e)}'
            }

    # Form validation failed
    error_messages = {field: errors for field, errors in form.errors.items()}
    return {
        'success': False,
        'message': 'Please correct the errors below.',
        'errors': error_messages
    }
